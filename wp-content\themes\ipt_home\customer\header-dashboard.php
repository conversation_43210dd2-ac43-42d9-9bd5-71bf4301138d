
<?php
// <PERSON><PERSON><PERSON> tra quyền truy cập
if (!is_user_logged_in()) {
    // Người dùng chưa đăng nhập, chuyển hướng đến trang đăng nhập
    wp_redirect(home_url().'?page_id=41');
    exit;
} else {
    $current_user = wp_get_current_user();
    $user_roles = (array) $current_user->roles;

    // Kiểm tra vai trò người dùng
    // if (!in_array('administrator', $user_roles)) {
    //     // Người dùng có vai trò customer, cho phép truy cập
    //     // Tiếp tục hiển thị trang dashboard
    // } else
    if (in_array('administrator', $user_roles)) {
        // Người dùng là administrator thì di chuyển đến wp-admin dashboard của wordpress mặc định
        wp_redirect(admin_url());
        exit();
    } 
    // else {
    //     // Người dùng có vai trò <PERSON>, không cho phép truy cập
    //     wp_redirect(home_url());
    //     exit();
    // }
}
?>
<header class="flex items-center justify-between h-[48px] bg-db-dark border-b border-neutral-light">
  
  <!-- Logo + menu -->
  <div class="flex items-center space-x-6 h-full">
    
    <!-- Menu -->
    <nav class="flex items-center space-x-4 h-full">
      <a href="<?php echo home_url(); ?>" class="flex items-center">
         <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/img/dashboard-logo.png" alt="Weaveform Customer Site" class="w-30 pl-3">
      </a>
      <a href="<?php echo site_url('/customer/dashboard'); ?>" class="text-white text-sm font-normal hover:text-brand-main transition border-l-2 border-neutral-light flex items-center pl-3 h-full">Dashboard</a>
      <a href="<?php echo site_url('/customer/website'); ?>" class="text-white text-sm font-normal hover:text-brand-main transition border-l-2 border-neutral-light flex items-center pl-3 h-full">My Websites</a>
      <!-- <button class="text-black text-sm font-normal px-3 py-1 rounded-xl ml-2 bg-db-green hover:bg-db-green  hover:opacity-80 transition">Upgrade</button> -->
    </nav>
  </div>
  <!-- Search + icons -->
  <div class="flex items-center space-x-4 pr-10">
    <!-- search input -->
    <!-- <div class="relative w-full max-w-[520px]">
      <input
         type="text"
         placeholder="Search for tools, apps & more..."
         class="w-full !pl-12 pr-4 py-2 !rounded-full !bg-transparent border border-white text-base text-white placeholder:text-neutral-light focus:outline-none focus:border-white focus:ring-0 transition"
      />
      <span class="absolute left-4 top-1/2 -translate-y-1/2 text-white">
         <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <circle cx="11" cy="11" r="8" stroke-width="2"/>
            <path d="M21 21l-4.35-4.35" stroke-width="2" stroke-linecap="round"/>
         </svg>
      </span>
    </div> -->
    <!-- search input -->
    <div class="relative w-full max-w-[520px]">
      <!-- <form id="dashboard-search-form" action="<?php echo site_url('/customer/create_website/'); ?>" method="get">
        <input
           type="text"
           name="keyword"
           id="dashboard-search"
           placeholder="Search for tools, apps & more..."
           value="<?php if(isset($_GET['keyword'])) echo $_GET['keyword']; ?>"
           class="w-full !pl-12 pr-4 py-2 !rounded-full !bg-transparent border border-white text-base text-white placeholder:text-neutral-light focus:outline-none focus:border-white focus:ring-0 transition"
        />
        <button type="submit" class="absolute left-4 top-1/2 -translate-y-1/2 text-white hover:text-white/80 !bg-transparent border-0 p-0 ">
           <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <circle cx="11" cy="11" r="8" stroke-width="2"/>
              <path d="M21 21l-4.35-4.35" stroke-width="2" stroke-linecap="round"/>
           </svg>
        </button>
      </form> -->
    </div>
    <!-- <a class="w-8 h-8 flex items-center cursor-pointer !text-white hover:!text-white justify-center rounded-full bg-transparent hover:bg-transparent transition">
      <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M18 5.5C19.1046 5.5 20 6.39543 20 7.5V15.5C20 16.6046 19.1046 17.5 18 17.5H12L8.62469 20.2002C8.19343 20.5453 7.56414 20.4753 7.21913 20.0441C7.07728 19.8668 7 19.6464 7 19.4194V17.5H6C4.89543 17.5 4 16.6046 4 15.5V7.5C4 6.39543 4.89543 5.5 6 5.5H18ZM18 6.5H6C5.48716 6.5 5.06449 6.88604 5.00673 7.38338L5 7.5V15.5C5 16.0128 5.38604 16.4355 5.88338 16.4933L6 16.5H8V19.4194L11.6492 16.5H18C18.5128 16.5 18.9355 16.114 18.9933 15.6166L19 15.5V7.5C19 6.98716 18.614 6.56449 18.1166 6.50673L18 6.5Z" fill="white"/>
      </svg>

    </a>
    <a class="w-8 h-8 flex items-center cusor-pointer !text-white hover:!text-white justify-center rounded-full bg-transparent hover:bg-transparent transition">
      <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.5 4.5C12.3284 4.5 13 5.17157 13 6V6.708L13.1929 6.76645C15.4021 7.47942 17 9.55311 17 12V14.882C17 15.5637 17.5141 15.9675 17.7236 16.0528C18.6312 16.5112 19 17.379 19 18.118V18.5001C19 19.0523 18.5523 19.5 18.0001 19.5001L13.9499 19.5004C13.7181 20.6413 12.7093 21.5 11.5 21.5C10.2907 21.5 9.2819 20.6413 9.0501 19.5004L4.99991 19.5001C4.44766 19.5 4 19.0523 4 18.5001V18.118C4 17.3017 4.43045 16.5509 5.12384 16.1364C5.70795 15.8547 6 15.4365 6 14.882V12C6 9.48221 7.69181 7.35957 10.0006 6.70685L10 6C10 5.17157 10.6716 4.5 11.5 4.5ZM12.9144 19.5007H10.0856C10.2917 20.0829 10.8472 20.5 11.5 20.5C12.1528 20.5 12.7083 20.0829 12.9144 19.5007ZM11.5 5.5C11.2239 5.5 11 5.72386 11 6L11.0003 7.52743C8.75018 7.77602 7 9.68362 7 12V14.882C7 15.6983 6.56955 16.4491 5.87616 16.8636L5.76098 16.9232C5.47658 17.0799 5 17.4239 5 18.118V18.5H18V18.118C18 17.4216 17.5177 17.0793 17.2764 16.9472C16.5954 16.6303 16 15.8188 16 14.882V12C16 9.68397 14.2503 7.77659 12.0007 7.52754L12 6C12 5.72386 11.7761 5.5 11.5 5.5Z" fill="white"/>
      </svg>


    </a> -->
    <div class="relative mx-4" id="account_dropdown">
      <a class="flex items-center space-x-2 focus:outline-none !bg-transparent hover:bg-transparent transition" id="account_info">
        <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/img/user-avatar.png" alt="Avatar" class="w-8 h-8 rounded-full">
        <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/img/chevron.png" alt="Avatar" class="w-8 h-8 rounded-full">
      </a>
      
      <!-- Dropdown menu -->
      <div class="absolute right-0 mt-2 w-48 bg-white !rounded-md shadow-xl py-1 z-50 hidden" id="account_dropdown_menu">
        <a href="<?php echo home_url().'/customer/edit_info'; ?>" class="block px-4 py-2 text-sm !text-gray-700 hover:bg-gray-100">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            Edit info
          </div>
        </a>
        <a href="<?php echo wp_logout_url(home_url()); ?>" id="logout-link" class="block px-4 py-2 text-sm !text-gray-700 hover:bg-gray-100">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            Logout
          </div>
        </a>
      </div>
    </div>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        const accountInfo = document.getElementById('account_info');
        const dropdownMenu = document.getElementById('account_dropdown_menu');
        
        // Toggle dropdown when clicking on account icon
        accountInfo.addEventListener('click', function(e) {
          e.stopPropagation();
          dropdownMenu.classList.toggle('hidden');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
          if (!document.getElementById('account_dropdown').contains(e.target)) {
            dropdownMenu.classList.add('hidden');
          }
        });
        
        // Close dropdown when pressing escape key
        document.addEventListener('keydown', function(e) {
          if (e.key === 'Escape') {
            dropdownMenu.classList.add('hidden');
          }
        });
        
        // Không cần xử lý sự kiện click cho logout-link nữa
        // Để nó hoạt động theo cách mặc định của WordPress
      });
    </script>
  </div>
</header>
