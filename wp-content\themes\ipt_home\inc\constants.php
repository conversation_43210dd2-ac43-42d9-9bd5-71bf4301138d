<?php
// File: inc/constants.php

// GraphQL API Configuration
define('GRAPHQL_API_URL', 'https://api-weaveform.ip-tribe.com/graphql');
define('GRAPHQL_TOKEN', 'ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys');

// Legacy constants for backward compatibility
define('IPT_API_URL', 'https://api-weaveform.ip-tribe.com/graphql');
define('IPT_API_KEY', 'ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys');

// Transaction Status IDs
define('IPT_STATUS_ID_PENDING', 1);
define('IPT_STATUS_ID_ACTIVE', 2);

define('IPT_HOME_FEATURE_LIST', [
    [
        'label' => 'Storage Limit',
        'type'  => 'number',
        'unit'  => 'GB',
    ],
    [
        'label' => 'Custom Domain Enabled',
        'type'  => 'checkbox',
        'desc'  => 'Website address of your choice e.g. www.BestShoeEver.com',
    ],
    [
        'label' => '1 Year Free Domain Enabled',
        'type'  => 'checkbox',
    ],
    [
        'label' => 'SSL Enabled',
        'type'  => 'checkbox',
    ],
    [
        'label' => 'AI Assisted Content Enabled',
        'type'  => 'checkbox',
    ],
    [
        'label' => 'Mobile Optimized Enabled',
        'type'  => 'checkbox',
    ],
    [
        'label'   => 'Backup Frequency',
        'type'    => 'dropdown',
        'options' => ['None', 'Daily', 'Weekly', 'Every 10 days'],
        'desc'    => 'Frequency',
    ],
    [
        'label' => 'Ads Enabled',
        'type'  => 'checkbox',
        'desc'  => 'Show Weaveform Ads',
    ],
    [
        'label' => 'Contact Form Enabled',
        'type'  => 'checkbox',
    ],
    [
        'label' => 'Firewall/Scanner Enabled',
        'type'  => 'checkbox',
    ],
    [
        'label'   => 'Customer Care Level',
        'type'    => 'dropdown',
        'options' => ['Nil', 'Email (1 day)', 'Priority Support'],
        'desc'    => 'Support Level',
    ],
]);

// Define status_id for all project
define('IPT_STATUS_ID', [
        'PENDING' => 1,
        'ACTIVE' => 2,
        'INACTIVE' => 3
    ]
);

// Define industry/category list
define('IPT_HOME_INDUSTRY_LIST', [
    'Animals',
    'Art and design',
    'Beauty',
    'Business',
    'Cars',
    'Coming soon',
    'Community and non-profit',
    'Education',
    'Entertainment',
    'Events',
    'Fashion',
    'Fitness',
    'Food',
    'Health',
    'Home & Décor',
    'Home services',
    'Music',
    'Personal & CV',
    'Personal Blog',
    'Photography',
    'Podcast',
    'Professional services',
    'Real estate',
    'Restaurants',
    'Travel',
    'Weddings',
    'Writers'
]);
