<?php

// Thêm rewrite rule cho slug /customer
add_action('init', function() {
    // Các route con cho customer
    add_rewrite_rule('^customer/?$', 'index.php?customer_page=dashboard', 'top');
    add_rewrite_rule('^customer/dashboard/?$', 'index.php?customer_page=dashboard', 'top');
    add_rewrite_rule('^customer/website/?$', 'index.php?customer_page=websites', 'top');
    add_rewrite_rule('^customer/create_website/?$', 'index.php?customer_page=create_website', 'top');
    add_rewrite_rule('^customer/settings/?$', 'index.php?customer_page=settings', 'top');
    add_rewrite_rule('^customer/account/?$', 'index.php?customer_page=account', 'top');
    add_rewrite_rule('^customer/create_payment/?$', 'index.php?customer_page=create_payment', 'top');
    add_rewrite_rule('^customer/subscription/?$', 'index.php?customer_page=subscription', 'top');
    add_rewrite_rule('^customer/edit_info/?$', 'index.php?customer_page=info', 'top');
    add_rewrite_rule('^customer/seo_management/?$', 'index.php?customer_page=seo_management', 'top');
    add_rewrite_rule('^customer/search_domain/?$', 'index.php?customer_page=search_domain', 'top');
    add_rewrite_rule('^customer/create_domain_payment/?$', 'index.php?customer_page=create_domain_payment', 'top');
    add_rewrite_rule('^customer/domain_success/?$', 'index.php?customer_page=domain_success', 'top');
    add_rewrite_rule('^customer/domain_management/?$', 'index.php?customer_page=domain_management', 'top');
    add_rewrite_rule('^customer/smtp_configuration/?$', 'index.php?customer_page=smtp_config', 'top');

    // clear cache after register new rewrite rule
    flush_rewrite_rules(true);
});

// Đăng ký query var mới
add_filter('query_vars', function($vars) {
    $vars[] = 'customer_page';
    return $vars;
});

// Điều hướng tới template khi truy cập /customer
add_action('template_redirect', function() {
    $page = get_query_var('customer_page');
    if ($page) {
        $template = get_stylesheet_directory() . '/customer/' . $page . '.php';
        if (file_exists($template)) {
            include $template;
            exit;
        } else {
            // Nếu không có file, trả về 404
            global $wp_query;
            $wp_query->set_404();
            status_header(404);
            nocache_headers();
            include get_query_template('404');
            exit;
        }
    }
});


/**
 * Xóa giỏ hàng WooCommerce
 * 
 * Hàm này có thể được gọi ở bất kỳ đâu để xóa toàn bộ giỏ hàng
 */
function ipt_empty_cart() {
    if (function_exists('WC')) {
        WC()->cart->empty_cart();
    }
}

// Thêm AJAX endpoint để cập nhật thông tin người dùng WordPress
add_action('wp_ajax_update_user_profile', 'designer_update_user_profile');
add_action('wp_ajax_nopriv_update_user_profile', 'designer_update_user_profile');

function designer_update_user_profile() {
    // Kiểm tra nonce bảo mật
    check_ajax_referer('update_user_profile_nonce', 'security');
    
    // Lấy ID người dùng hiện tại
    $user_id = get_current_user_id();
    
    // Kiểm tra xem người dùng đã đăng nhập chưa
    if (!$user_id) {
        wp_send_json_error(['message' => 'User not logged in']);
        return;
    }
    
    // Cập nhật thông tin người dùng
    $first_name = isset($_POST['first_name']) ? sanitize_text_field($_POST['first_name']) : '';
    $last_name = isset($_POST['last_name']) ? sanitize_text_field($_POST['last_name']) : '';
    $phone = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    
    // Cập nhật thông tin cơ bản
    if (!empty($first_name)) {
        update_user_meta($user_id, 'first_name', $first_name);
    }
    
    if (!empty($last_name)) {
        update_user_meta($user_id, 'last_name', $last_name);
    }
    
    if (!empty($phone)) {
        update_user_meta($user_id, 'phone', $phone);
    }
    
    // Cập nhật mật khẩu nếu có
    if (!empty($password)) {
        wp_update_user([
            'ID' => $user_id,
            'user_pass' => $password
        ]);
    }
    
    wp_send_json_success(['message' => 'User profile updated successfully']);
}