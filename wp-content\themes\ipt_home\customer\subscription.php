<?php
// Handle downgrade success message
if (isset($_GET['downgrade_success']) && $_GET['downgrade_success'] === '1') {
    echo '<div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4" role="alert">';
    echo '<strong>Success!</strong> Your plan has been cancelled. You will keep access until your expiration date.';
    echo '</div>';
}

// Handle payment success redirect from Stripe
if (isset($_GET['payment_intent']) && isset($_GET['redirect_status']) && $_GET['redirect_status'] === 'succeeded') {
    $payment_intent_id = sanitize_text_field($_GET['payment_intent']);

    error_log('Payment success detected on subscription page. Payment Intent: ' . $payment_intent_id);

    // Check if order already exists for this payment intent
    $existing_orders = wc_get_orders(array(
        'meta_key' => '_stripe_payment_intent',
        'meta_value' => $payment_intent_id,
        'limit' => 1
    ));

    if (empty($existing_orders)) {
        error_log('No existing order found for payment intent, creating new order...');

        // Try to retrieve payment intent from <PERSON><PERSON> to get amount
        try {
            // Get Stripe settings
            $stripe_settings = get_option('woocommerce_stripe_settings');
            $test_mode = isset($stripe_settings['testmode']) && $stripe_settings['testmode'] === 'yes';
            $secret_key = $test_mode ?
                $stripe_settings['test_secret_key'] :
                $stripe_settings['secret_key'];

            if (!empty($secret_key)) {
                // Load WC_Stripe_API
                if (!class_exists('WC_Stripe_API')) {
                    $stripe_api_path = WP_PLUGIN_DIR . '/woocommerce-gateway-stripe/includes/class-wc-stripe-api.php';
                    if (file_exists($stripe_api_path)) {
                        require_once($stripe_api_path);
                    }
                }

                if (class_exists('WC_Stripe_API')) {
                    WC_Stripe_API::set_secret_key($secret_key);
                    $payment_intent = WC_Stripe_API::request([], 'payment_intents/' . $payment_intent_id);

                    if (!empty($payment_intent->amount) && !empty($payment_intent->metadata)) {
                        error_log('Retrieved payment intent data: amount=' . $payment_intent->amount . ', metadata=' . print_r($payment_intent->metadata, true));

                        // Create order from payment intent data
                        $order = wc_create_order();

                        // Set customer info
                        $current_user = wp_get_current_user();
                        $order->set_customer_id($current_user->ID);
                        $order->set_billing_email($current_user->user_email);

                        // Try to find product from metadata or determine from amount
                        $product_id = null;
                        $amount_dollars = $payment_intent->amount / 100;

                        if (!empty($payment_intent->metadata->product_id)) {
                            $product_id = $payment_intent->metadata->product_id;
                            error_log('Using product from metadata: ' . $product_id);
                        } else {
                            // Determine product based on payment amount
                            error_log('No product in metadata, determining from amount: $' . $amount_dollars);

                            // First, let's verify our product setup
                            error_log('=== PRODUCT VERIFICATION ===');

                            // Check SKU "free"
                            $free_product_id = wc_get_product_id_by_sku('free');
                            if ($free_product_id) {
                                $free_product = wc_get_product($free_product_id);
                                $free_type_id = $free_product->get_meta('subscription_type_id');
                                error_log("SKU 'free': ID={$free_product_id}, Name={$free_product->get_name()}, Type={$free_type_id}, Price={$free_product->get_price()}");
                            } else {
                                error_log("❌ SKU 'free' not found!");
                            }

                            // Check SKU "premium"
                            $premium_product_id = wc_get_product_id_by_sku('premium');
                            if ($premium_product_id) {
                                $premium_product = wc_get_product($premium_product_id);
                                $premium_type_id = $premium_product->get_meta('subscription_type_id');
                                error_log("SKU 'premium': ID={$premium_product_id}, Name={$premium_product->get_name()}, Type={$premium_type_id}, Price={$premium_product->get_price()}");

                                // Auto-fix: If premium product doesn't have subscription_type_id = 2, set it
                                if ($premium_type_id !== '2') {
                                    error_log("🔧 Auto-fixing: Setting premium product subscription_type_id to 2 (Basic)");
                                    update_post_meta($premium_product_id, 'subscription_type_id', '2');
                                    error_log("✅ Premium product updated to subscription_type_id = 2");
                                }
                            } else {
                                error_log("❌ SKU 'premium' not found!");
                            }

                            // Check all subscription products
                            $all_subscription_products = wc_get_products([
                                'status' => 'publish',
                                'limit' => -1,
                                'meta_query' => [
                                    [
                                        'key' => 'subscription_type_id',
                                        'compare' => 'EXISTS'
                                    ]
                                ]
                            ]);

                            error_log('All subscription products:');
                            foreach ($all_subscription_products as $p) {
                                $type_id = $p->get_meta('subscription_type_id');
                                $sku = $p->get_sku();
                                error_log("- ID: {$p->get_id()}, Name: {$p->get_name()}, SKU: {$sku}, Type: {$type_id}, Price: {$p->get_price()}");
                            }

                            if ($amount_dollars >= 50 && $amount_dollars <= 60) {
                                // Basic plan (~$55) - should use SKU "premium" with subscription_type_id = 2

                                // First, ensure Premium product has correct subscription_type_id
                                $premium_product_id = wc_get_product_id_by_sku('premium');
                                if ($premium_product_id) {
                                    $premium_product = wc_get_product($premium_product_id);
                                    $current_type_id = $premium_product->get_meta('subscription_type_id');
                                    error_log("Premium product (ID: $premium_product_id) current type_id: '$current_type_id'");

                                    // Force set to type 2 if not already set
                                    if ($current_type_id !== '2') {
                                        error_log("🔧 Fixing Premium product subscription_type_id from '$current_type_id' to '2'");
                                        update_post_meta($premium_product_id, 'subscription_type_id', '2');

                                        // Clear any caches
                                        wp_cache_delete($premium_product_id, 'posts');
                                        wp_cache_delete($premium_product_id, 'post_meta');

                                        // Reload product
                                        $premium_product = wc_get_product($premium_product_id);
                                        $new_type_id = $premium_product->get_meta('subscription_type_id');
                                        error_log("✅ Premium product updated. New type_id: '$new_type_id'");
                                    }
                                }

                                // Now query for Basic plan products
                                $products = wc_get_products([
                                    'status' => 'publish',
                                    'limit' => -1, // Get all to debug
                                    'meta_query' => [
                                        [
                                            'key' => 'subscription_type_id',
                                            'value' => '2', // Basic plan (SKU premium)
                                            'compare' => '='
                                        ]
                                    ]
                                ]);
                                error_log('Looking for Basic plan (Type 2), found: ' . count($products) . ' products');

                                // Debug: Log all found products
                                foreach ($products as $p) {
                                    $type_id = $p->get_meta('subscription_type_id');
                                    error_log("- Found product: ID={$p->get_id()}, Name={$p->get_name()}, SKU={$p->get_sku()}, Type={$type_id}");
                                }

                                // Use Premium product directly if query fails
                                if (empty($products) && $premium_product_id) {
                                    $products = [wc_get_product($premium_product_id)];
                                    error_log('Fallback: Using premium product by SKU: ' . $premium_product_id);
                                } elseif (!empty($products)) {
                                    // Filter to get the Premium SKU product if multiple found
                                    $premium_products = array_filter($products, function($p) {
                                        return $p->get_sku() === 'premium';
                                    });
                                    if (!empty($premium_products)) {
                                        $products = array_values($premium_products);
                                        error_log('Filtered to Premium SKU product: ' . $products[0]->get_id());
                                    }
                                }
                            } elseif ($amount_dollars >= 90 && $amount_dollars <= 110) {
                                // Premium plan (~$99)
                                $products = wc_get_products([
                                    'status' => 'publish',
                                    'limit' => 1,
                                    'meta_query' => [
                                        [
                                            'key' => 'subscription_type_id',
                                            'value' => '3', // Premium plan
                                            'compare' => '='
                                        ]
                                    ]
                                ]);
                                error_log('Looking for Premium plan (Type 3), found: ' . count($products) . ' products');
                            } else {
                                // Default to Basic plan
                                $products = wc_get_products([
                                    'status' => 'publish',
                                    'limit' => 1,
                                    'meta_query' => [
                                        [
                                            'key' => 'subscription_type_id',
                                            'value' => '2', // Basic plan
                                            'compare' => '='
                                        ]
                                    ]
                                ]);
                                error_log('Default to Basic plan (Type 2), found: ' . count($products) . ' products');
                            }

                            if (!empty($products)) {
                                $product_id = $products[0]->get_id();
                                error_log('Selected product based on amount: ' . $product_id . ' (' . $products[0]->get_name() . ')');
                            } else {
                                error_log('No matching product found! Falling back to first available subscription product');
                                if (!empty($all_subscription_products)) {
                                    $product_id = $all_subscription_products[0]->get_id();
                                    error_log('Using fallback product: ' . $product_id . ' (' . $all_subscription_products[0]->get_name() . ')');
                                }
                            }
                        }

                        if ($product_id) {
                            $product = wc_get_product($product_id);
                            if ($product) {
                                $order->add_product($product, 1);
                                error_log('Added product to order: ' . $product->get_name());
                            }
                        }

                        // Set payment info
                        $order->set_payment_method('stripe');
                        $order->set_payment_method_title('Credit Card (Stripe)');
                        $order->update_meta_data('_stripe_payment_intent', $payment_intent_id);

                        // Set total from payment intent
                        $amount = $payment_intent->amount / 100; // Convert from cents
                        $order->set_total($amount);

                        // Complete payment
                        $order->payment_complete($payment_intent_id);
                        $order->add_order_note('Payment completed via Stripe. Payment Intent ID: ' . $payment_intent_id);
                        $order->save();

                        error_log('Order created successfully: #' . $order->get_id() . ' with total: ' . $order->get_total());

                        // Redirect to clean URL
                        wp_safe_redirect(site_url('/?customer_page=subscription'));
                        exit;
                    }
                }
            }
        } catch (Exception $e) {
            error_log('Error creating order from payment intent: ' . $e->getMessage());
        }
    } else {
        error_log('Order already exists for payment intent: ' . $existing_orders[0]->get_id());
    }
}
?>

<?php include get_stylesheet_directory() . '/customer/header-customer.php'; ?>
<?php include get_stylesheet_directory() . '/customer/header-dashboard.php'; ?>
<div class="flex min-h-screen bg-neutral-light">
   <?php include get_stylesheet_directory() . '/customer/sidebar.php'; ?>
   <div class="flex-1 flex flex-col min-h-screen">

      <!-- Subscription Management -->
      <main class="flex-1 p-4 bg-db-dark-2">
         <div class="bg-white rounded-lg shadow p-6">
            <!-- Header with navigation -->
            <!-- <div class="flex items-center mb-6">
               <div class="flex items-center text-sm text-gray-500">
                  <span>Settings</span>
                  <svg class="mx-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                  <span>Subscription management</span>
               </div>
            </div> -->
            <nav class="" aria-label="Breadcrumb">
               <ol class="flex items-center space-x text-sm list-none ml-0 mb-1">
                  <li>
                     <a href="<?php echo site_url('/customer/subscription'); ?>" class="font-normal hover:text-db-text text-db-text  text-14 ">Settings</a>
                  </li>
                  <li>
                     <span class="text-neutral-400 text-12">
                        <svg width="22" height="30" viewBox="0 0 22 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path fill-rule="evenodd" clip-rule="evenodd" d="M8 8.7L12.6 15L8 21.3L8.8 22L14 15L8.8 8L8 8.7Z" fill="#000624" fill-opacity="0.3"/>
                        </svg>
                     </span>
                  </li>
                  <li>
                     <a href="#" class="font-normal hover:text-db-text text-db-text text-14">Manage Subscription</a>
                  </li>
                  
               </ol>
            </nav>
            
            <!-- Title and action buttons -->
            <div class="flex justify-between items-center mb-8">
               <h1 class="text-2xl font-bold">Manage Subscription</h1>
               <div class="flex gap-2">
                  <!-- <button class="px-4 py-2 border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50">Cancel</button>
                  <button class="px-4 py-2 rounded-md bg-teal-500 text-white hover:bg-teal-600">Save</button> -->
               </div>
            </div>

            <!-- Payment Success Message -->
            <?php if (isset($_GET['payment_intent']) && isset($_GET['redirect_status']) && $_GET['redirect_status'] === 'succeeded'): ?>
            <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded" id="payment-success-message">
               <div class="flex items-center">
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <div>
                     <strong>Payment Successful!</strong>
                     <p class="text-sm mt-1">Your subscription has been activated successfully. Thank you for your payment!</p>
                  </div>
               </div>
            </div>
            <?php endif; ?>
            
            <!-- Current Subscription Section -->
            <div class="mb-8">
               <h2 class="text-lg font-semibold mb-4">Current Subscription</h2>
               <div class="border-t border-b border-gray-200 py-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                     <div>
                        <p class="text-sm text-gray-500 mb-1">Plan Type</p>
                        <p class="font-medium" id="plan-type"></p>
                     </div>
                     <div>
                        <p class="text-sm text-gray-500 mb-1" id="renewal-label">Expiration Date</p>
                        <p class="font-medium" id="renewal-date"></p>
                     </div>
                  </div>
                  
                  <!-- <div class="mt-4">
                     <p class="text-sm text-gray-500 mb-1">Credit Remaining</p>
                     <p class="font-medium" id="credit-remaining">[$Amount]</p>
                  </div> -->
               </div>
            </div>
            
            <!-- Payment Details Section -->
            <div class="mb-8">
               <h2 class="text-lg font-semibold mb-4">Payment Details</h2>
               <div class="border-t border-b border-gray-200 py-4">
                  <div class="flex justify-between items-center">
                     <div>
                        <p class="text-sm text-gray-500 mb-1">Payment Method</p>
                        <p class="font-medium" id="payment-method"></p>
                     </div>
                     <!-- <button class="px-4 py-2 rounded-md bg-teal-500 text-white hover:bg-teal-600" disabled>Manage Payment</button> -->
                  </div>
               </div>
            </div>
            
            <!-- Upgrade/Downgrade Options Section -->
            <div>
               <h2 class="text-lg font-semibold mb-4">Upgrade/Downgrade Options</h2>
               <div class="border-t border-gray-200 py-4">
                  <div class="flex justify-end gap-2" id="plan-actions">
                     <!-- Buttons will be dynamically shown based on plan type -->
                     <button id="downgrade-btn" class="px-4 py-2 rounded-md bg-red-100 text-red-600 hover:bg-red-200" style="display: none;">Downgrade Plan</button>
                     <button id="upgrade-btn" class="px-4 py-2 rounded-md bg-teal-500 text-white hover:bg-teal-600" style="display: none;">Upgrade Plan</button>
                  </div>
               </div>
            </div>
            
            <!-- Script to load subscription data -->
            <script>
               jQuery(document).ready(function($) {

                  // Handle payment success message
                  if ($('#payment-success-message').length > 0) {
                     // Auto-hide success message after 10 seconds
                     setTimeout(function() {
                        $('#payment-success-message').fadeOut(500);
                     }, 10000);

                     // Clean URL by removing payment parameters after showing message
                     setTimeout(function() {
                        const url = new URL(window.location);
                        url.searchParams.delete('payment_intent');
                        url.searchParams.delete('payment_intent_client_secret');
                        url.searchParams.delete('redirect_status');
                        window.history.replaceState({}, document.title, url.toString());
                     }, 2000);
                  }
                  // Fetch subscription data
                  function fetchSubscriptionData() {
                     $.ajax({
                        url: iptHomeAjax.ajax_url,
                        type: 'POST',
                        dataType: 'json',
                        data: {
                           action: 'ipt_get_subscription_data',
                           security: '<?php echo wp_create_nonce('ipt-subscription-nonce'); ?>'
                        },
                        success: function(response) {
                           if (response.success && response.data) {
                              // Update UI with subscription data
                              $('#plan-type').text(response.data.plan_type || '');
                              $('#renewal-date').text(response.data.renewal_date || '[MM/DD/YYYY]');
                              // $('#credit-remaining').text(response.data.credit_remaining || '[$Amount]');
                              $('#payment-method').text(response.data.payment_method || '...');
                           }
                        },
                        error: function(xhr, status, error) {
                           console.error('Error fetching subscription data:', error);
                        }
                     });
                  }
                  
                  // Fetch latest order data with Stripe payment
                  function fetchLatestOrderData() {
                     $.ajax({
                        url: iptHomeAjax.ajax_url,
                        type: 'POST',
                        dataType: 'json',
                        data: {
                           action: 'ipt_get_latest_stripe_order',
                           security: '<?php echo wp_create_nonce('ipt-subscription-nonce'); ?>'
                        },
                        success: function(response) {
                           console.log('=== SUBSCRIPTION PAGE DEBUG ===');
                           console.log('Full API Response:', response);
                           console.log('response.success:', response.success);
                           console.log('response.data:', response.data);

                           if (response.success && response.data) {
                              // Debug logging
                              console.log('Subscription data received:', response.data);

                              // Update UI with latest order data
                              $('#plan-type').text(response.data.plan_name || '[Trial / Basic / Premium]');

                              // Update renewal label and date based on trial status
                              const renewalLabel = response.data.renewal_label || 'Expired';
                              const renewalDate = response.data.renewal_date || '[MM/DD/YYYY]';
                              const isTrial = response.data.is_trial || false;

                              // Debug trial detection
                              console.log('Trial detection:', {
                                 is_trial: isTrial,
                                 order_total: response.data.order_total,
                                 is_trial_order_meta: response.data.is_trial_order_meta,
                                 subscription_type_id: response.data.subscription_type_id,
                                 renewal_label: renewalLabel
                              });

                              // Debug order selection logic
                              if (response.data.debug_orders) {
                                 console.log('All orders found:', response.data.debug_orders);
                                 console.log('Order selection logic:', response.data.selected_order_logic);
                              }

                              $('#renewal-label').text(renewalLabel);
                              $('#renewal-date').text(renewalDate);

                              // Add visual indicator for trial
                              if (isTrial) {
                                 $('#renewal-date').addClass('text-orange-600 font-semibold');
                                 $('#renewal-label').addClass('text-orange-600');
                                 console.log('Applied trial styling (orange)');
                              } else {
                                 $('#renewal-date').removeClass('text-orange-600 font-semibold').addClass('text-green-600');
                                 $('#renewal-label').removeClass('text-orange-600').addClass('text-green-600');
                                 console.log('Applied paid styling (green)');
                              }

                              $('#credit-remaining').text(response.data.amount || '[$Amount]');
                              $('#payment-method').text(response.data.payment_method || '');

                              // Check if plan is cancelled
                              const isPlanCancelled = response.data.is_plan_cancelled || false;
                              const expirationDate = response.data.expiration_date || response.data.renewal_date;
                              const today = new Date();
                              const expDate = new Date(expirationDate);
                              const isExpired = expDate <= today;

                              // Show appropriate button based on plan type and cancellation status
                              if (isPlanCancelled) {
                                 if (isExpired) {
                                    // Plan is cancelled and expired - show upgrade options
                                    $('#upgrade-btn').show();
                                    $('#downgrade-btn').hide();
                                    console.log('Showing upgrade button for expired cancelled plan');
                                 } else {
                                    // Plan is cancelled but not expired - show upgrade options (user can re-upgrade)
                                    $('#upgrade-btn').show();
                                    $('#downgrade-btn').hide();
                                    console.log('Showing upgrade button for cancelled plan (still active until expiration)');
                                 }
                              } else if (isTrial) {
                                 // Trial users can only upgrade
                                 $('#upgrade-btn').show();
                                 $('#downgrade-btn').hide();
                                 console.log('Showing upgrade button for trial');
                              } else if (!response.data.order_id || response.data.plan_name === '' || response.data.plan_name === '[Trial / Basic / Premium]') {
                                 // Users with no payment history - show upgrade button
                                 $('#upgrade-btn').show();
                                 $('#downgrade-btn').hide();
                                 console.log('Showing upgrade button for user with no payment history');

                                 // Set special flag for no-subscription users
                                 window.currentPlan = window.currentPlan || {};
                                 window.currentPlan.hasNoSubscription = true;
                              } else {
                                 // Non-trial users can only downgrade
                                 $('#downgrade-btn').show();
                                 $('#upgrade-btn').hide();
                                 console.log('Showing downgrade button for paid plan');
                              }

                              // Store plan info for button actions
                              window.currentPlan = {
                                 isTrial: isTrial,
                                 planName: response.data.plan_name,
                                 orderId: response.data.order_id,
                                 subscriptionPlanId: response.data.subscription_plan_id,
                                 renewalDate: renewalDate,
                                 trialDays: response.data.trial_days
                              };

                              // Log final plan info
                              console.log('Current plan info:', window.currentPlan);
                           } else {
                              // API returned success: false (e.g., "No Stripe orders found")

                              // Set empty values
                              $('#plan-type').text('');
                              $('#renewal-label').text('');
                              $('#renewal-date').text('');
                              $('#credit-remaining').text('');
                              $('#payment-method').text('');

                              // Show upgrade button for users with no subscription data
                              $('#upgrade-btn').show();
                              $('#downgrade-btn').hide();


                              // Set flag for no subscription
                              window.currentPlan = {
                                 hasNoSubscription: true
                              };

                           }
                        },
                        error: function(xhr, status, error) {
                           console.error('Error fetching latest order data:', error);

                           // Set default values if API fails - likely no subscription data
                           $('#plan-type').text('');
                           $('#renewal-label').text('');
                           $('#renewal-date').text('');
                           $('#credit-remaining').text('');
                           $('#payment-method').text('');

                           // Show upgrade button for users with no subscription data
                           $('#upgrade-btn').show();
                           $('#downgrade-btn').hide();
                           console.log('API failed - showing upgrade button for user with no subscription');

                           // Set flag for no subscription
                           window.currentPlan = {
                              hasNoSubscription: true
                           };
                        }
                     });
                  }
                  
                  // Load subscription data on page load
                  fetchLatestOrderData();
                  
                  // Handle manage payment button
                  $(document).on('click', 'button:contains("Manage Payment")', function() {
                     // Implement payment management functionality
                     console.log('Manage payment clicked');
                     // Redirect to payment management page
                     window.location.href = '<?php echo site_url('/?customer_page=payment_management'); ?>';
                  });
                  
                  // Handle upgrade button click
                  $('#upgrade-btn').on('click', function() {
                     if (window.currentPlan) {
                        // Check if user has no subscription
                        if (window.currentPlan.hasNoSubscription) {
                           console.log('Upgrade clicked for user with no subscription - redirecting to upgrade page');
                           window.location.href = '<?php echo esc_js(add_query_arg('customer_page', 'upgrade', home_url())); ?>';

                           return;
                        }

                        console.log('Upgrade clicked for trial plan:', window.currentPlan.planName);

                        // Redirect to upgrade page with current plan info
                        const upgradeUrl = '<?php echo esc_js(add_query_arg('customer_page', 'upgrade', home_url())); ?>' +
                                         '&action=upgrade' +
                                         '&current_plan=' + encodeURIComponent(window.currentPlan.subscriptionPlanId || '') +
                                         '&order_id=' + encodeURIComponent(window.currentPlan.orderId || '') +
                                         '&trial_days=' + encodeURIComponent(window.currentPlan.trialDays || '');

                        window.location.href = upgradeUrl;
                     }
                  });

                  // Handle downgrade button click
                  $('#downgrade-btn').on('click', function() {
                     if (window.currentPlan) {
                        console.log('Downgrade clicked for plan:', window.currentPlan.planName);

                        // Show confirmation dialog
                        const expirationDate = window.currentPlan.renewalDate || 'your current billing period ends';
                        const confirmMessage = `Are you sure you want to downgrade your plan?\n\n` +
                                             `• You will keep access to your current plan until ${expirationDate}\n` +
                                             `• You won't be charged again after expiration\n` +
                                             `• Your plan will be marked as "Cancelled"\n` +
                                             `• You can upgrade again after your plan expires\n\n` +
                                             `Do you want to proceed with the downgrade?`;

                        if (confirm(confirmMessage)) {
                           // User confirmed, proceed with direct downgrade processing
                           const downgradeUrl = '<?php echo esc_js(add_query_arg('customer_page', 'upgrade', home_url())); ?>' +
                                              '&process_downgrade=1' +
                                              '&order_id=' + encodeURIComponent(window.currentPlan.orderId || '');

                           window.location.href = downgradeUrl;
                        } else {
                           console.log('Downgrade cancelled by user');
                        }
                     }
                  });
                  
                  // Note: Downgrade handling is now done by the #downgrade-btn click handler above
               });
            </script>
         </div>
      </main>
   </div>
</div>

<?php include get_stylesheet_directory() . '/customer/footer-customer.php'; ?>
