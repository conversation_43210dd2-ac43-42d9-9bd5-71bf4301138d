<?php
/**
 * The header for our theme
 *
 * @package ipt_home
 */

?>
<?php 
    $args = wp_parse_args(
        $args,
        array(
            'hide_menu' => false,
        )
    );

    /**
     * Option fields
     */
    $logo_icon = get_field('logo_icon', 'option');
    $logo_text = get_field('logo_text', 'option');
?>
<!doctype html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Weaveform</title>
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&family=Roboto:wght@400;500;700&display=swap"
        rel="stylesheet" />

    
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>
<div class="flex flex-col items-start w-full bg-stone-50">
<?php if (!$args['hide_menu']) : ?>   
<header
class="flex relative justify-between items-center p-[16px] px-1 lg:px-20 w-full bg-white h-[84px] shadow-elevation-bottom-100">
        <div class="flex items-start md:items-center flex-shrink-0">
            <a href="<?php echo home_url(); ?>" class="flex items-center">
                <img src="<?php echo $logo_icon; ?>"
                    alt="Weaveform Logo" class="h-auto w-auto max-h-[84px]"/>
                <img src="<?php echo $logo_text; ?>"
                    alt="Weaveform Logo" class="h-auto w-auto max-h-[84px] ml-2 hidden md:block"/>
            </a>
        </div><!-- .site-branding / logo -->
        <div class="hidden flex md:h-[84px] h-screen md:h-auto w-full md:block md:w-full md:mt-[20px] absolute md:static left-0 top-[84px] z-50 md:z-auto" id="navbar-header">
            <ul class="flex items-center gap-[5px] lg:gap-[24px] md:justify-center flex-col border border-gray-100 bg-white w-full h-full md:h-auto md:flex-row bg-white">
                <?php ipt_home_custom_nav_menu('main-menu'); ?><!-- .site-navigation -->
                <li class="block sm:hidden text-center">
                    <?php if (is_user_logged_in()) : ?>
                        <?php 
                            $current_user = wp_get_current_user();
                            $user_roles = (array) $current_user->roles;
                            $dashboard_url = in_array('administrator', $user_roles) ? admin_url() : home_url('customer/dashboard');
                        ?>
                        <button class="align-middle text-16 font-semibold bg-brand-main hover:!bg-brand-main focus:!bg-brand-main rounded-[8px] w-[196px] h-[48px] !text-primary-main" onclick="window.location.href='<?php echo $dashboard_url; ?>'">
                            Dashboard
                        </button>
                    <?php else : ?>
                        <button class="align-middle mb-4 text-16 font-semibold bg-brand-main hover:!bg-brand-main focus:!bg-brand-main rounded-[8px] w-[196px] h-[48px] !text-primary-main" onclick="window.location.href='<?php echo site_url('sign-in'); ?>'">
                            Login
                        </button>
                        <button class="align-middle text-16 font-semibold bg-brand-main hover:!bg-brand-main focus:!bg-brand-main rounded-[8px]  w-[196px] h-[48px] !text-primary-main" onclick="window.location.href='<?php echo site_url('sign-up'); ?>'">
                            Get Started
                        </button>
                    <?php endif; ?>
                </li>
            </ul>
        </div>

        <div class="flex items-center">
            <button data-collapse-toggle="navbar-header" type="button" class="sm:!rounded-none !shadow-none bg-transparent hover:!bg-transparent inline-flex items-center p-2 w-10 h-10 justify-center text-sm md:hidden" aria-controls="navbar-default" aria-expanded="true">
                <span class="sr-only">Menu</span>
                
                <!-- Icon hamburger -->
                <svg class="hamburger-icon transition-transform duration-300 ease-in-out" width="20" height="14" viewBox="0 0 20 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M0 1C0 0.447715 0.447715 0 1 0H19C19.5523 0 20 0.447715 20 1C20 1.55228 19.5523 2 19 2H1C0.447715 2 0 1.55228 0 1ZM0 7C0 6.44772 0.447715 6 1 6H19C19.5523 6 20 6.44772 20 7C20 7.55228 19.5523 8 19 8H1C0.447715 8 0 7.55228 0 7ZM0 13C0 12.4477 0.447715 12 1 12H13C13.5523 12 14 12.4477 14 13C14 13.5523 13.5523 14 13 14H1C0.447715 14 0 13.5523 0 13Z" fill="#001D6C"/>
                </svg>
                
                <!-- Icon X - ẩn mặc định -->
                <svg class="close-icon transition-transform duration-300 ease-in-out hidden" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M4.29289 4.29289C4.68342 3.90237 5.31658 3.90237 5.70711 4.29289L10 8.58579L14.2929 4.29289C14.6834 3.90237 15.3166 3.90237 15.7071 4.29289C16.0976 4.68342 16.0976 5.31658 15.7071 5.70711L11.4142 10L15.7071 14.2929C16.0976 14.6834 16.0976 15.3166 15.7071 15.7071C15.3166 16.0976 14.6834 16.0976 14.2929 15.7071L10 11.4142L5.70711 15.7071C5.31658 16.0976 4.68342 16.0976 4.29289 15.7071C3.90237 15.3166 3.90237 14.6834 4.29289 14.2929L8.58579 10L4.29289 5.70711C3.90237 5.31658 3.90237 4.68342 4.29289 4.29289Z" fill="#001D6C"/>
                </svg>
            </button>
            <?php if (is_user_logged_in()) : ?>
                <?php 
                    $current_user = wp_get_current_user();
                    $user_roles = (array) $current_user->roles;
                    $dashboard_url = in_array('administrator', $user_roles) ? admin_url() : home_url('customer/dashboard');
                ?>
                <button class="align-middle text-[16px] font-semibold bg-brand-main hover:!bg-brand-main focus:!bg-brand-main rounded-[8px] w-[100px] mr-2 lg:w-[196px] h-[48px] !text-primary-main max-md:hidden" onclick="window.location.href='<?php echo $dashboard_url; ?>'">
                    Dashboard
                </button>
            <?php else : ?>
                <a href="<?php echo site_url('sign-in'); ?>" class="text-[16px] font-semibold text-brand-main hover:!text-brand-main focus:!text-brand-main max-md:hidden px-6 !outline-none active:outline-none"> Login</a>
                <button class="align-middle text-[16px] font-semibold bg-brand-main hover:!bg-brand-main focus:!bg-brand-main rounded-[8px] w-[100px] mr-2 lg:w-[196px] h-[48px] !text-primary-main max-md:hidden" onclick="window.location.href='<?php echo site_url('sign-up'); ?>'">
                    Get Started
                </button>
            <?php endif; ?>
        </div><!-- #menu-buttons -->
    </header><!-- #masthead --> 
<?php endif; ?>
