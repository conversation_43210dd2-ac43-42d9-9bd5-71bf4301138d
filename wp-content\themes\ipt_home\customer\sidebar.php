<?php 
  // Get current menu from query var (works with both ?customer_page=X and /customer/X/ URLs)
  $current_menu = get_query_var('customer_page') ?: ($_GET['customer_page'] ?? 'dashboard');
?>
<aside class="w-[150px] md:w-[250px] min-h-screen bg-db-dark text-white flex flex-col py-2 px-2">
  <!-- Home -->
  <a href="<?php echo site_url('/customer/dashboard'); ?>" class="flex items-center gap-3 px-4 py-2 rounded-lg outline-none focus:outline-none active:outline-none hover:bg-db-dark-light hover:text-white transition text-14 text-white mb-2">
    <!-- Home icon -->
    <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M9.47745 0.3366C9.81975 0.24392 10.1805 0.24392 10.5228 0.3366C10.9202 0.44419 11.2548 0.706848 11.5218 0.91648C11.5473 0.936494 11.5721 0.956025 11.5964 0.974891L18.3787 6.25002C18.4048 6.27032 18.4307 6.29043 18.4564 6.31038C18.833 6.60265 19.1648 6.86015 19.4121 7.19445C19.6291 7.48784 19.7907 7.81835 19.8891 8.16974C20.0012 8.57016 20.0007 8.99017 20.0002 9.46692C20.0002 9.49944 20.0001 9.53223 20.0001 9.5653V16.8388C20.0002 17.3659 20.0002 17.8207 19.9696 18.1953C19.9373 18.5906 19.866 18.9838 19.6732 19.3622C19.3855 19.9267 18.9266 20.3856 18.3621 20.6732C17.9837 20.866 17.5905 20.9374 17.1952 20.9697C16.8206 21.0003 16.3658 21.0002 15.8387 21.0002H4.16157C3.63443 21.0002 3.17968 21.0003 2.80511 20.9697C2.40977 20.9374 2.01655 20.866 1.63817 20.6732C1.07368 20.3856 0.61474 19.9267 0.32712 19.3622C0.134326 18.9838 0.0630099 18.5906 0.030709 18.1953C0.000105144 17.8207 0.000120642 17.3659 0.000138524 16.8388L0.000139478 9.5653C0.000139478 9.53223 0.000104072 9.49944 6.89057e-05 9.46691C-0.000446317 8.99017 -0.000900149 8.57016 0.11118 8.16974C0.209542 7.81834 0.371188 7.48783 0.58818 7.19445C0.835439 6.86015 1.16725 6.60265 1.54389 6.31036C1.56958 6.29042 1.59549 6.27032 1.62159 6.25002L8.40389 0.97489C8.42815 0.956024 8.45303 0.936493 8.47852 0.916479C8.74553 0.706848 9.08008 0.44419 9.47745 0.3366ZM9.99284 2.28177C9.91769 2.33198 9.81868 2.40822 9.63177 2.55359L2.84947 7.82872C2.3514 8.21611 2.25864 8.29926 2.19615 8.38375C2.12382 8.48154 2.06994 8.59171 2.03715 8.70885C2.00883 8.81005 2.00014 8.93431 2.00014 9.5653V16.8002C2.00014 17.3768 2.00092 17.7491 2.02407 18.0324C2.04626 18.3041 2.08394 18.4048 2.10913 18.4542C2.20501 18.6424 2.35799 18.7954 2.54615 18.8912C2.59559 18.9164 2.69631 18.9541 2.96798 18.9763C3.25131 18.9995 3.62359 19.0002 4.20014 19.0002H15.8001C16.3767 19.0002 16.749 18.9995 17.0323 18.9763C17.304 18.9541 17.4047 18.9164 17.4541 18.8912C17.6423 18.7954 17.7953 18.6424 17.8911 18.4542C17.9163 18.4048 17.954 18.3041 17.9762 18.0324C17.9994 17.7491 18.0001 17.3768 18.0001 16.8002V9.5653C18.0001 8.93431 17.9915 8.81005 17.9631 8.70885C17.9303 8.59171 17.8765 8.48154 17.8041 8.38375C17.7416 8.29926 17.6489 8.21611 17.1508 7.82872L10.3685 2.5536C10.1816 2.40822 10.0826 2.33198 10.0074 2.28177C10.0049 2.28007 10.0025 2.27846 10.0001 2.27694C9.99782 2.27846 9.99539 2.28007 9.99284 2.28177ZM5.00014 16.0002C5.00014 15.4479 5.44785 15.0002 6.00014 15.0002H14.0001C14.5524 15.0002 15.0001 15.4479 15.0001 16.0002C15.0001 16.5525 14.5524 17.0002 14.0001 17.0002H6.00014C5.44785 17.0002 5.00014 16.5525 5.00014 16.0002Z" fill="#EEEEEE"/>
   </svg>
    Dashboard
  </a>

  

<!-- Website (dropdown) -->
  <div class="relative mb-2">
    <button type="button" class="flex items-center gap-3 px-4 py-2 rounded-lg w-full !bg-db-dark hover:bg-db-dark-light hover:text-white transition font-medium text-white focus:text-white outline-none focus:outline-none active:outline-none text-14" onclick="toggleSidebarMenu('website-menu')">
      <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
         <path fill-rule="evenodd" clip-rule="evenodd" d="M9.47745 0.3366C9.81975 0.24392 10.1805 0.24392 10.5228 0.3366C10.9202 0.44419 11.2548 0.706848 11.5218 0.91648C11.5473 0.936494 11.5721 0.956025 11.5964 0.974891L18.3787 6.25002C18.4048 6.27032 18.4307 6.29043 18.4564 6.31038C18.833 6.60265 19.1648 6.86015 19.4121 7.19445C19.6291 7.48784 19.7907 7.81835 19.8891 8.16974C20.0012 8.57016 20.0007 8.99017 20.0002 9.46692C20.0002 9.49944 20.0001 9.53223 20.0001 9.5653V16.8388C20.0002 17.3659 20.0002 17.8207 19.9696 18.1953C19.9373 18.5906 19.866 18.9838 19.6732 19.3622C19.3855 19.9267 18.9266 20.3856 18.3621 20.6732C17.9837 20.866 17.5905 20.9374 17.1952 20.9697C16.8206 21.0003 16.3658 21.0002 15.8387 21.0002H4.16157C3.63443 21.0002 3.17968 21.0003 2.80511 20.9697C2.40977 20.9374 2.01655 20.866 1.63817 20.6732C1.07368 20.3856 0.61474 19.9267 0.32712 19.3622C0.134326 18.9838 0.0630099 18.5906 0.030709 18.1953C0.000105144 17.8207 0.000120642 17.3659 0.000138524 16.8388L0.000139478 9.5653C0.000139478 9.53223 0.000104072 9.49944 6.89057e-05 9.46691C-0.000446317 8.99017 -0.000900149 8.57016 0.11118 8.16974C0.209542 7.81834 0.371188 7.48783 0.58818 7.19445C0.835439 6.86015 1.16725 6.60265 1.54389 6.31036C1.56958 6.29042 1.59549 6.27032 1.62159 6.25002L8.40389 0.97489C8.42815 0.956024 8.45303 0.936493 8.47852 0.916479C8.74553 0.706848 9.08008 0.44419 9.47745 0.3366ZM9.99284 2.28177C9.91769 2.33198 9.81868 2.40822 9.63177 2.55359L2.84947 7.82872C2.3514 8.21611 2.25864 8.29926 2.19615 8.38375C2.12382 8.48154 2.06994 8.59171 2.03715 8.70885C2.00883 8.81005 2.00014 8.93431 2.00014 9.5653V16.8002C2.00014 17.3768 2.00092 17.7491 2.02407 18.0324C2.04626 18.3041 2.08394 18.4048 2.10913 18.4542C2.20501 18.6424 2.35799 18.7954 2.54615 18.8912C2.59559 18.9164 2.69631 18.9541 2.96798 18.9763C3.25131 18.9995 3.62359 19.0002 4.20014 19.0002H15.8001C16.3767 19.0002 16.749 18.9995 17.0323 18.9763C17.304 18.9541 17.4047 18.9164 17.4541 18.8912C17.6423 18.7954 17.7953 18.6424 17.8911 18.4542C17.9163 18.4048 17.954 18.3041 17.9762 18.0324C17.9994 17.7491 18.0001 17.3768 18.0001 16.8002V9.5653C18.0001 8.93431 17.9915 8.81005 17.9631 8.70885C17.9303 8.59171 17.8765 8.48154 17.8041 8.38375C17.7416 8.29926 17.6489 8.21611 17.1508 7.82872L10.3685 2.5536C10.1816 2.40822 10.0826 2.33198 10.0074 2.28177C10.0049 2.28007 10.0025 2.27846 10.0001 2.27694C9.99782 2.27846 9.99539 2.28007 9.99284 2.28177ZM5.00014 16.0002C5.00014 15.4479 5.44785 15.0002 6.00014 15.0002H14.0001C14.5524 15.0002 15.0001 15.4479 15.0001 16.0002C15.0001 16.5525 14.5524 17.0002 14.0001 17.0002H6.00014C5.44785 17.0002 5.00014 16.5525 5.00014 16.0002Z" fill="#EEEEEE"/>
      </svg>
      Website
      <svg class="w-4 h-4 ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
         <path d="M9 5l7 7-7 7" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
    <div id="website-menu" class="mt-2 ml-2 bg-db-dark-light rounded-xl py-3 px-0 shadow-lg transition <?php if($current_menu != 'websites' && $current_menu != 'create_website'){ ?>hidden<?php } ?>">
      <ul class="list-none ml-3">
        <li>
          <a href="<?php echo site_url('/customer/website'); ?>" class="flex items-center px-2 py-2 text-neutral-light hover:text-white hover:bd-db-dark-light transition <?php if($current_menu == 'websites'){ ?>border-l-4 border-white<?php }else{ ?>border-l-2 border-neutral-light<?php } ?>  focus:text-white outline-none focus:outline-none active:outline-none text-13">Manage Sites</a>
        </li>
        <li>
          <a href="<?php echo site_url('/customer/create_website'); ?>" class="flex items-center px-2 py-2 text-neutral-light hover:text-white hover:bd-db-dark-light transition <?php if($current_menu == 'create_website'){ ?>border-l-4 border-white<?php }else{ ?>border-l-2 border-neutral-light<?php } ?> border-l-2 border-neutral-light  focus:text-white outline-none focus:outline-none active:outline-none text-13">Create new site</a>
        </li>
      
      </ul>
    </div>
  </div>

  <!-- Settings -->
  <div class="relative mb-2">
    <button type="button" class="flex items-center gap-3 px-4 py-2 rounded-lg w-full !bg-db-dark hover:bg-db-dark-light hover:text-white transition font-medium text-white focus:text-white outline-none focus:outline-none active:outline-none text-14" onclick="toggleSidebarMenu('subscription-menu')">
      <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
         <path fill-rule="evenodd" clip-rule="evenodd" d="M9.47745 0.3366C9.81975 0.24392 10.1805 0.24392 10.5228 0.3366C10.9202 0.44419 11.2548 0.706848 11.5218 0.91648C11.5473 0.936494 11.5721 0.956025 11.5964 0.974891L18.3787 6.25002C18.4048 6.27032 18.4307 6.29043 18.4564 6.31038C18.833 6.60265 19.1648 6.86015 19.4121 7.19445C19.6291 7.48784 19.7907 7.81835 19.8891 8.16974C20.0012 8.57016 20.0007 8.99017 20.0002 9.46692C20.0002 9.49944 20.0001 9.53223 20.0001 9.5653V16.8388C20.0002 17.3659 20.0002 17.8207 19.9696 18.1953C19.9373 18.5906 19.866 18.9838 19.6732 19.3622C19.3855 19.9267 18.9266 20.3856 18.3621 20.6732C17.9837 20.866 17.5905 20.9374 17.1952 20.9697C16.8206 21.0003 16.3658 21.0002 15.8387 21.0002H4.16157C3.63443 21.0002 3.17968 21.0003 2.80511 20.9697C2.40977 20.9374 2.01655 20.866 1.63817 20.6732C1.07368 20.3856 0.61474 19.9267 0.32712 19.3622C0.134326 18.9838 0.0630099 18.5906 0.030709 18.1953C0.000105144 17.8207 0.000120642 17.3659 0.000138524 16.8388L0.000139478 9.5653C0.000139478 9.53223 0.000104072 9.49944 6.89057e-05 9.46691C-0.000446317 8.99017 -0.000900149 8.57016 0.11118 8.16974C0.209542 7.81834 0.371188 7.48783 0.58818 7.19445C0.835439 6.86015 1.16725 6.60265 1.54389 6.31036C1.56958 6.29042 1.59549 6.27032 1.62159 6.25002L8.40389 0.97489C8.42815 0.956024 8.45303 0.936493 8.47852 0.916479C8.74553 0.706848 9.08008 0.44419 9.47745 0.3366ZM9.99284 2.28177C9.91769 2.33198 9.81868 2.40822 9.63177 2.55359L2.84947 7.82872C2.3514 8.21611 2.25864 8.29926 2.19615 8.38375C2.12382 8.48154 2.06994 8.59171 2.03715 8.70885C2.00883 8.81005 2.00014 8.93431 2.00014 9.5653V16.8002C2.00014 17.3768 2.00092 17.7491 2.02407 18.0324C2.04626 18.3041 2.08394 18.4048 2.10913 18.4542C2.20501 18.6424 2.35799 18.7954 2.54615 18.8912C2.59559 18.9164 2.69631 18.9541 2.96798 18.9763C3.25131 18.9995 3.62359 19.0002 4.20014 19.0002H15.8001C16.3767 19.0002 16.749 18.9995 17.0323 18.9763C17.304 18.9541 17.4047 18.9164 17.4541 18.8912C17.6423 18.7954 17.7953 18.6424 17.8911 18.4542C17.9163 18.4048 17.954 18.3041 17.9762 18.0324C17.9994 17.7491 18.0001 17.3768 18.0001 16.8002V9.5653C18.0001 8.93431 17.9915 8.81005 17.9631 8.70885C17.9303 8.59171 17.8765 8.48154 17.8041 8.38375C17.7416 8.29926 17.6489 8.21611 17.1508 7.82872L10.3685 2.5536C10.1816 2.40822 10.0826 2.33198 10.0074 2.28177C10.0049 2.28007 10.0025 2.27846 10.0001 2.27694C9.99782 2.27846 9.99539 2.28007 9.99284 2.28177ZM5.00014 16.0002C5.00014 15.4479 5.44785 15.0002 6.00014 15.0002H14.0001C14.5524 15.0002 15.0001 15.4479 15.0001 16.0002C15.0001 16.5525 14.5524 17.0002 14.0001 17.0002H6.00014C5.44785 17.0002 5.00014 16.5525 5.00014 16.0002Z" fill="#EEEEEE"/>
      </svg>

      Setting
      <svg class="w-4 h-4 ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
         <path d="M9 5l7 7-7 7" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
    <div id="subscription-menu" class="mt-2 ml-2 bg-db-dark-light rounded-xl py-3 px-0 shadow-lg transition <?php if($current_menu != 'subscription' && $current_menu != 'seo_management' && $current_menu != 'domain_management' && $current_menu != 'smtp_configuration'){ ?>hidden<?php } ?>">
      <ul class="list-none ml-3">
        <li>
          <a href="<?php echo site_url('/customer/subscription'); ?>" class="flex items-center px-2 py-2 text-neutral-light hover:text-white hover:bg-db-dark-light transition text-13 <?php if($current_menu == 'subscription'){ ?>border-l-4 border-white<?php }else{ ?>border-l-2 border-neutral-light<?php } ?>  focus:text-white outline-none focus:outline-none active:outline-none">Manage Subscription</a>
        </li>
        <!-- <li>
          <a href="<?php echo site_url('/customer/seo_management'); ?>" class="flex items-center px-2 py-2 text-neutral-light hover:text-white hover:bg-db-dark-light transition text-13 <?php if($current_menu == 'seo_management'){ ?>border-l-4 border-white<?php }else{ ?>border-l-2 border-neutral-light<?php } ?>  focus:text-white outline-none focus:outline-none active:outline-none">SEO management</a>
        </li>-->
        <li>
          <a href="<?php echo site_url('/customer/domain_management'); ?>" class="flex items-center px-2 py-2 text-neutral-light hover:text-white hover:bg-db-dark-light transition text-13 <?php if($current_menu == 'domain_management'){ ?>border-l-4 border-white<?php }else{ ?>border-l-2 border-neutral-light<?php } ?>  focus:text-white outline-none focus:outline-none active:outline-none">Manage Domain</a>
        </li> 
        <li>
          <a href="<?php echo site_url('/customer/smtp_configuration'); ?>" class="flex items-center px-2 py-2 text-neutral-light hover:text-white hover:bg-db-dark-light transition text-13 <?php if($current_menu == 'smtp_configuration'){ ?>border-l-4 border-white<?php }else{ ?>border-l-2 border-neutral-light<?php } ?>  focus:text-white outline-none focus:outline-none active:outline-none">SMTP Configuration</a>
        </li> 
      </ul>
    </div>
  </div>

  <!-- <div class="relative mb-2">
    <button type="button" class="flex items-center gap-3 px-4 py-2 rounded-lg w-full !bg-db-dark hover:bg-db-dark-light hover:text-white transition font-medium text-white focus:text-white outline-none focus:outline-none active:outline-none text-14" onclick="toggleSidebarMenu('edit_info-menu')">
      <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
         <path fill-rule="evenodd" clip-rule="evenodd" d="M9.47745 0.3366C9.81975 0.24392 10.1805 0.24392 10.5228 0.3366C10.9202 0.44419 11.2548 0.706848 11.5218 0.91648C11.5473 0.936494 11.5721 0.956025 11.5964 0.974891L18.3787 6.25002C18.4048 6.27032 18.4307 6.29043 18.4564 6.31038C18.833 6.60265 19.1648 6.86015 19.4121 7.19445C19.6291 7.48784 19.7907 7.81835 19.8891 8.16974C20.0012 8.57016 20.0007 8.99017 20.0002 9.46692C20.0002 9.49944 20.0001 9.53223 20.0001 9.5653V16.8388C20.0002 17.3659 20.0002 17.8207 19.9696 18.1953C19.9373 18.5906 19.866 18.9838 19.6732 19.3622C19.3855 19.9267 18.9266 20.3856 18.3621 20.6732C17.9837 20.866 17.5905 20.9374 17.1952 20.9697C16.8206 21.0003 16.3658 21.0002 15.8387 21.0002H4.16157C3.63443 21.0002 3.17968 21.0003 2.80511 20.9697C2.40977 20.9374 2.01655 20.866 1.63817 20.6732C1.07368 20.3856 0.61474 19.9267 0.32712 19.3622C0.134326 18.9838 0.0630099 18.5906 0.030709 18.1953C0.000105144 17.8207 0.000120642 17.3659 0.000138524 16.8388L0.000139478 9.5653C0.000139478 9.53223 0.000104072 9.49944 6.89057e-05 9.46691C-0.000446317 8.99017 -0.000900149 8.57016 0.11118 8.16974C0.209542 7.81834 0.371188 7.48783 0.58818 7.19445C0.835439 6.86015 1.16725 6.60265 1.54389 6.31036C1.56958 6.29042 1.59549 6.27032 1.62159 6.25002L8.40389 0.97489C8.42815 0.956024 8.45303 0.936493 8.47852 0.916479C8.74553 0.706848 9.08008 0.44419 9.47745 0.3366ZM9.99284 2.28177C9.91769 2.33198 9.81868 2.40822 9.63177 2.55359L2.84947 7.82872C2.3514 8.21611 2.25864 8.29926 2.19615 8.38375C2.12382 8.48154 2.06994 8.59171 2.03715 8.70885C2.00883 8.81005 2.00014 8.93431 2.00014 9.5653V16.8002C2.00014 17.3768 2.00092 17.7491 2.02407 18.0324C2.04626 18.3041 2.08394 18.4048 2.10913 18.4542C2.20501 18.6424 2.35799 18.7954 2.54615 18.8912C2.59559 18.9164 2.69631 18.9541 2.96798 18.9763C3.25131 18.9995 3.62359 19.0002 4.20014 19.0002H15.8001C16.3767 19.0002 16.749 18.9995 17.0323 18.9763C17.304 18.9541 17.4047 18.9164 17.4541 18.8912C17.6423 18.7954 17.7953 18.6424 17.8911 18.4542C17.9163 18.4048 17.954 18.3041 17.9762 18.0324C17.9994 17.7491 18.0001 17.3768 18.0001 16.8002V9.5653C18.0001 8.93431 17.9915 8.81005 17.9631 8.70885C17.9303 8.59171 17.8765 8.48154 17.8041 8.38375C17.7416 8.29926 17.6489 8.21611 17.1508 7.82872L10.3685 2.5536C10.1816 2.40822 10.0826 2.33198 10.0074 2.28177C10.0049 2.28007 10.0025 2.27846 10.0001 2.27694C9.99782 2.27846 9.99539 2.28007 9.99284 2.28177ZM5.00014 16.0002C5.00014 15.4479 5.44785 15.0002 6.00014 15.0002H14.0001C14.5524 15.0002 15.0001 15.4479 15.0001 16.0002C15.0001 16.5525 14.5524 17.0002 14.0001 17.0002H6.00014C5.44785 17.0002 5.00014 16.5525 5.00014 16.0002Z" fill="#EEEEEE"/>
      </svg>

      Account
      <svg class="w-4 h-4 ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
         <path d="M9 5l7 7-7 7" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
    <div id="edit_info-menu" class="mt-2 ml-2 bg-db-dark-light rounded-xl py-3 px-0 shadow-lg transition <?php if($current_menu != 'info' && $current_menu != 'info'){ ?>hidden<?php } ?>">
      <ul class="list-none ml-3">
        <li>
          <a href="<?php echo site_url('/customer/edit_info'); ?>" class="flex items-center px-2 py-2 text-neutral-light hover:text-white hover:bg-db-dark-light transition text-13 <?php if($current_menu == 'info'){ ?>border-l-4 border-white<?php }else{ ?>border-l-2 border-neutral-light<?php } ?>  focus:text-white outline-none focus:outline-none active:outline-none">Edit account information</a>
        </li>
      </ul>
    </div>
  </div> -->
</aside>

<script>
function toggleSidebarMenu(id) {
  const menu = document.getElementById(id);
  menu.classList.toggle('hidden');
}
</script>
