<?php include get_stylesheet_directory() . '/customer/header-customer.php'; ?>
<?php include get_stylesheet_directory() . '/customer/header-dashboard.php'; ?>

<style>
/* Custom dropdown arrow styling */
.select-custom {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 16px center;
    background-repeat: no-repeat;
    background-size: 20px 20px;
}

.select-custom:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%232BA990' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
}

/* Template selection styling */
.template-card {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.template-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-card.selected {
    border-color: #2BA990;
    box-shadow: 0 4px 12px rgba(43, 169, 144, 0.2);
}

.template-card.selected::after {
    content: '✓';
    position: absolute;
    top: 8px;
    right: 8px;
    background: #2BA990;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
}

/* Ensure selected button styling takes precedence */
.template-choose-btn.bg-brand-main {
    background-color: #2BA990 !important;
    border-color: #2BA990 !important;
    color: white !important;
}

.template-choose-btn.bg-brand-main:hover {
    background-color: #2BA990 !important;
    opacity: 0.9;
}

/* Loading Overlay Styles */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-content {
    background: white;
    padding: 40px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #2BA990;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-title {
    font-size: 24px;
    font-weight: bold;
    color: #0A1734;
    margin-bottom: 10px;
}

.loading-message {
    font-size: 16px;
    color: #666;
    margin-bottom: 20px;
}

.loading-steps {
    text-align: left;
    margin-top: 20px;
}

.loading-step {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
}

.loading-step-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.loading-step.completed .loading-step-icon {
    background: #2BA990;
    color: white;
}

.loading-step.active .loading-step-icon {
    background: #2BA990;
    color: white;
    animation: pulse 1.5s ease-in-out infinite;
}

.loading-step.pending .loading-step-icon {
    background: #f3f3f3;
    color: #999;
    border: 2px solid #ddd;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
</style>
<div class="flex min-h-screen bg-neutral-light">
   <?php include get_stylesheet_directory() . '/customer/sidebar.php'; ?>
   <div class="flex-1 flex flex-col min-h-screen">
       
      
      <!-- Website -->
      <main class="flex-1 p-4 bg-db-dark-2">
            <nav class="" aria-label="Breadcrumb">
               <ol class="flex items-center space-x text-sm list-none ml-0 mb-1">
                  <li>
                     <a href="<?php echo site_url('/customer/smtp_config'); ?>" class="font-normal hover:text-db-text text-db-text  text-14 ">Settings</a>
                  </li>
                  <li>
                     <span class="text-neutral-400 text-12">
                        <svg width="22" height="30" viewBox="0 0 22 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path fill-rule="evenodd" clip-rule="evenodd" d="M8 8.7L12.6 15L8 21.3L8.8 22L14 15L8.8 8L8 8.7Z" fill="#000624" fill-opacity="0.3"/>
                        </svg>
                     </span>
                  </li>
                  <li>
                     <a href="#" class="font-normal hover:text-db-text text-db-text text-14">SMTP Configuration</a>
                  </li>
                  
               </ol>
            </nav>

         <!-- Page Title -->
         <h1 class="text-2xl font-semibold text-gray-900 mb-8">SMTP Configuration</h1>


         
         <div class="bg-white rounded-lg shadow p-4">
          
            <!-- Danh sách website sẽ render ở đây -->
            <!-- <div class="text-neutral-medium text-center py-12">No websites found.</div> -->
            <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
               <!-- Cột trái: 4/12 -->
               <div class="xl:col-span-12 col-span-12 relative overflow-hidden min-h-[220px] flex flex-col justify-start">
                  <form class="flex flex-col gap-8 mt-2" id="formCreateWebsite">
                     <!-- BSMTP Host -->
                     <div class="flex flex-col gap-2">
                        <label for="smtp_host" class="text-primary-main text-base font-medium">
                          SMTP Host
                        </label>
                        <input type="text"
                           id="smtp_host"
                           name="smtp_host"
                           placeholder="smtp.google.com"
                           class="w-full !px-[16px] !py-[12px] !h-[48px] rounded-lg !bg-ipt-bg-1
                                    focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                    placeholder:text-gray-400
                                    transition-all duration-200">
                        <div id="smtp_host_error" class="text-red-500 text-sm mt-1 hidden"></div>
                     </div>

                     

                     

                     <!--  SMTP Port -->
                     <div class="flex flex-col gap-2">
                        <label for="smtp_port" class="text-primary-main text-base font-medium">
                           SMTP Port
                        </label>
                        <input type="text"
                           id="smtp_port"
                           name="smtp_port"
                           placeholder="576"
                           class="w-full !px-[16px] !py-[12px] !h-[48px] rounded-lg !bg-ipt-bg-1
                                    focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                    placeholder:text-gray-400
                                    transition-all duration-200">
                        <div id="smtp_port_error" class="text-red-500 text-sm mt-1 hidden"></div>
                     </div>

                     <!--  SSL/TLS -->
                     <div class="flex flex-col gap-2">
                        <label for="ssl_tls" class="text-primary-main text-base font-medium">
                          SSL/TLS
                        </label>
                        <div class="flex gap-6">
                           <label class="flex items-center gap-2 cursor-pointer">
                              <input type="radio"
                                     id="ssl_true"
                                     name="ssl_tls"
                                     value="true"
                                     class="w-4 h-4 text-brand-main bg-gray-100 border-gray-300 focus:ring-brand-main focus:ring-2">
                              <span class="text-gray-700">True</span>
                           </label>
                           <label class="flex items-center gap-2 cursor-pointer">
                              <input type="radio"
                                     id="ssl_false"
                                     name="ssl_tls"
                                     value="false"
                                     class="w-4 h-4 text-brand-main bg-gray-100 border-gray-300 focus:ring-brand-main focus:ring-2">
                              <span class="text-gray-700">False</span>
                           </label>
                        </div>
                        <div id="ssl_tls_error" class="text-red-500 text-sm mt-1 hidden"></div>
                     </div>
                     <!-- SMTP Username -->
                     <div class="flex flex-col gap-2">
                        <label for="smtp_username" class="text-primary-main text-base font-medium">
                         SMTP Username
                        </label>
                        <input type="text"
                           id="smtp_username"
                           name="smtp_username"
                           placeholder="<EMAIL>"
                           autocomplete="off"
                           class="w-full !px-[16px] !py-[12px] !h-[48px] rounded-lg !bg-ipt-bg-1
                                    focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                    placeholder:text-gray-400
                                    transition-all duration-200">
                        <div id="smtp_username_error" class="text-red-500 text-sm mt-1 hidden"></div>
                     </div>
                     <!-- SMTP Password -->
                     <div class="flex flex-col gap-2">
                        <label for="smtp_password" class="text-primary-main text-base font-medium">
                          SMTP Password
                        </label>
                        <input type="password"
                           id="smtp_password"
                           name="smtp_password"
                           placeholder=""
                           autocomplete="off"
                           class="w-full !px-[16px] !py-[12px] !h-[48px] rounded-lg !bg-ipt-bg-1
                                    focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                    placeholder:text-gray-400
                                    transition-all duration-200">
                        <div id="smtp_password_error" class="text-red-500 text-sm mt-1 hidden"></div>
                     </div>
                     <div class="flex gap-4 justify-start">
                        <button type="button"
                                       id="testSMTPBtn"
                                       class="py-3 px-6 text-sm rounded-lg bg-gray-200 hover:!bg-gray-300 active:!bg-gray-300 focus:!bg-gray-300  text-gray-700 font-normal
                                          transition-all duration-200"
                                       onclick="showTestSMTPModal()"
                                       >
                              Test SMTP
                        </button>
                        <button type="button"
                                 id="saveSMTPBtn"
                                 class="py-3 px-6 rounded-lg bg-brand-main hover:bg-brand-main/80 text-primary-main font-normal
                                    transition-all duration-200"
                                 onclick="saveSMTPConfiguration()"
                                 >
                           Save
                        </button>
                     </div>
                  </form>
               </div>

           
         </div>

         
      </main>

   </div>
</div>

<!-- Test SMTP Modal -->
<div id="testSMTPModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
   <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
      <div class="flex justify-between items-center mb-4">
         <h3 class="text-lg font-semibold text-gray-900">Test SMTP Configuration</h3>
         <button onclick="closeTestSMTPModal()" class="text-dark hover:text-dark-600 bg-transparent shadow-none hover:bg-transparent">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
               <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
         </button>
      </div>

      <div class="space-y-4">
         <!-- Subject Field -->
         <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
            <input type="text"
                   id="test_subject"
                   placeholder="Test SMTP Configuration"
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main">
            <div id="test_subject_error" class="text-red-500 text-sm mt-1 hidden"></div>
         </div>

         <!-- Email Field -->
         <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Send To</label>
            <input type="email"
                   id="test_email"
                   readonly
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600">
            <div id="test_email_error" class="text-red-500 text-sm mt-1 hidden"></div>
         </div>

         <!-- Content Field -->
         <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Message Content</label>
            <textarea id="test_content"
                      rows="4"
                      placeholder="This is a test email to verify SMTP configuration is working correctly."
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main resize-vertical"></textarea>
            <div id="test_content_error" class="text-red-500 text-sm mt-1 hidden"></div>
         </div>
      </div>

      <div class="flex gap-3 mt-6">
         <!-- <button onclick="closeTestSMTPModal()"
                 class="flex-1 py-2 px-2 text-sm border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
            Cancel
         </button> -->
         <button onclick="sendTestEmail()"
                 id="sendTestBtn"
                 class="flex-1 py-2 px-2 text-sm bg-brand-main text-white rounded-lg hover:bg-brand-main/80 transition-colors">
            Send Test
         </button>
      </div>
   </div>
</div>

<script>
function toggleMenu(id) {
  const menu = document.getElementById(id);
  menu.classList.toggle('hidden');
}

// SMTP Configuration Page
document.addEventListener('DOMContentLoaded', function() {
   // Load SMTP configuration on page load
   loadSMTPConfiguration();

});

/**
 * Execute GraphQL mutation using WordPress AJAX
 */
function executeGraphQLMutation(mutation, variables = {}) {
    return new Promise((resolve, reject) => {
        jQuery.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'ipt_home_graphql',
                query: mutation,
                variables: JSON.stringify(variables)
            },
            success: function(response) {
                if (response.errors && response.errors.length > 0) {
                    reject(new Error(response.errors[0].message || 'GraphQL error occurred'));
                } else if (response.data) {
                    resolve(response.data);
                } else {
                    reject(new Error('Invalid response from server'));
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                reject(new Error('Failed to connect to server'));
            }
        });
    });
}

/**
 * Load SMTP configuration from GraphQL API
 */
async function loadSMTPConfiguration() {
    try {
        // Get customer ID from user meta
        const customerId = <?php echo json_encode(get_user_meta(get_current_user_id(), 'customer_id', true)); ?>;

        if (!customerId) {
            console.log('No customer ID found');
            return;
        }

        const query = `
            query Webhooks_smtp_view_by_customer {
                webhooks_smtp_view_by_customer(customer_id: ${customerId}) {
                    id
                    smtp_config
                }
            }
        `;

        const data = await executeGraphQLMutation(query);
        console.log('SMTP Configuration Response:', data);

        if (data.webhooks_smtp_view_by_customer) {
            const smtpData = data.webhooks_smtp_view_by_customer;
            populateSMTPForm(smtpData);
        }

    } catch (error) {
        console.error('Error loading SMTP configuration:', error);
    }
}

/**
 * Populate SMTP form with loaded data
 */
function populateSMTPForm(smtpData) {
    if (!smtpData || !smtpData.smtp_config) {
        console.log('No SMTP configuration found');
        return;
    }

    const config = smtpData.smtp_config;
    console.log('Populating form with config:', config);

    // Populate form fields
    if (config.host) {
        document.getElementById('smtp_host').value = config.host;
    }

    if (config.port) {
        document.getElementById('smtp_port').value = config.port;
    }

    if (config.username) {
        document.getElementById('smtp_username').value = config.username;
    }

    if (config.password) {
        document.getElementById('smtp_password').value = config.password;
    }

    // Handle SSL/TLS radio buttons
    if (config.ssl !== undefined) {
        const sslValue = config.ssl ? 'true' : 'false';
        const radioButton = document.querySelector(`input[name="ssl_tls"][value="${sslValue}"]`);
        if (radioButton) {
            radioButton.checked = true;
        }
    }

    console.log('SMTP form populated successfully');
}

/**
 * Validate SMTP form fields
 */
function validateSMTPForm() {
    let isValid = true;

    // Clear previous errors
    clearAllErrors();

    // Get form values
    const host = document.getElementById('smtp_host').value.trim();
    const port = document.getElementById('smtp_port').value.trim();
    const username = document.getElementById('smtp_username').value.trim();
    const password = document.getElementById('smtp_password').value.trim();
    const sslTls = document.querySelector('input[name="ssl_tls"]:checked');

    // Validate SMTP Host
    if (!host) {
        showFieldError('smtp_host_error', 'SMTP Host is required');
        isValid = false;
    }

    // Validate SMTP Port
    if (!port) {
        showFieldError('smtp_port_error', 'SMTP Port is required');
        isValid = false;
    } else if (!/^\d+$/.test(port) || parseInt(port) < 1 || parseInt(port) > 65535) {
        showFieldError('smtp_port_error', 'Please enter a valid port number (1-65535)');
        isValid = false;
    }

    // Validate SSL/TLS selection
    if (!sslTls) {
        showFieldError('ssl_tls_error', 'Please select SSL/TLS option');
        isValid = false;
    }

    // Validate SMTP Username
    if (!username) {
        showFieldError('smtp_username_error', 'SMTP Username is required');
        isValid = false;
    }

    // Validate SMTP Password
    if (!password) {
        showFieldError('smtp_password_error', 'SMTP Password is required');
        isValid = false;
    }

    return isValid;
}

/**
 * Show field error message
 */
function showFieldError(errorId, message) {
    const errorElement = document.getElementById(errorId);
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.classList.remove('hidden');
    }
}

/**
 * Clear all error messages
 */
function clearAllErrors() {
    const errorElements = [
        'smtp_host_error',
        'smtp_port_error',
        'ssl_tls_error',
        'smtp_username_error',
        'smtp_password_error'
    ];

    errorElements.forEach(errorId => {
        const errorElement = document.getElementById(errorId);
        if (errorElement) {
            errorElement.textContent = '';
            errorElement.classList.add('hidden');
        }
    });
}

/**
 * Save SMTP configuration
 */
async function saveSMTPConfiguration() {
    try {
        // Validate form first
        if (!validateSMTPForm()) {
            return;
        }

        // Disable save button and show loading state
        const saveBtn = document.getElementById('saveSMTPBtn');
        const originalText = saveBtn.textContent;
        saveBtn.disabled = true;
        saveBtn.textContent = 'Saving...';

        // Get customer ID
        const customerId = <?php echo json_encode(get_user_meta(get_current_user_id(), 'customer_id', true)); ?>;

        if (!customerId) {
            throw new Error('Customer ID not found. Please login again.');
        }

        // Get form values
        const host = document.getElementById('smtp_host').value.trim();
        const port = parseInt(document.getElementById('smtp_port').value.trim());
        const username = document.getElementById('smtp_username').value.trim();
        const password = document.getElementById('smtp_password').value.trim();
        const ssl = document.querySelector('input[name="ssl_tls"]:checked').value === 'true';

        // Prepare GraphQL mutation
        const mutation = `
            mutation Webhooks_smtp_save($input: WebhookSmtpSaveInputDto!) {
                webhooks_smtp_save(input: $input) {
                    id
                }
            }
        `;

        const variables = {
            input: {
                customer_id: parseInt(customerId),
                smtp_config: {
                    host: host,
                    port: port,
                    ssl: ssl,
                    username: username,
                    password: password
                }
            }
        };

        console.log('Saving SMTP configuration:', variables);

        // Use executeGraphQLMutation function
        const data = await executeGraphQLMutation(mutation, variables);
        console.log('SMTP Save Response:', data);

        if (data.webhooks_smtp_save && data.webhooks_smtp_save.id) {
            // Success
            console.log('SMTP configuration saved successfully with ID:', data.webhooks_smtp_save.id);

            // Show success message (you can customize this)
            alert('SMTP configuration saved successfully!');

            // Optionally reload the configuration to show updated data
            loadSMTPConfiguration();
        } else {
            throw new Error('Invalid response from server');
        }

    } catch (error) {
        console.error('Error saving SMTP configuration:', error);
        alert('Failed to save SMTP configuration: ' + error.message);
    } finally {
        // Re-enable save button
        const saveBtn = document.getElementById('saveSMTPBtn');
        saveBtn.disabled = false;
        saveBtn.textContent = 'Save';
    }
}

/**
 * Show Test SMTP Modal
 */
function showTestSMTPModal() {
    // First validate SMTP form fields
    if (!validateSMTPFormForTest()) {
        return;
    }

    // Get customer email and populate the modal
    const customerEmail = '<?php echo wp_get_current_user()->user_email; ?>';
    document.getElementById('test_email').value = customerEmail;

    // Set default values
    document.getElementById('test_subject').value = 'Test SMTP Configuration';
    document.getElementById('test_content').value = 'This is a test email to verify SMTP configuration is working correctly.';

    // Clear any previous errors
    clearTestModalErrors();

    // Show modal
    document.getElementById('testSMTPModal').classList.remove('hidden');
}

/**
 * Close Test SMTP Modal
 */
function closeTestSMTPModal() {
    document.getElementById('testSMTPModal').classList.add('hidden');
    clearTestModalErrors();
}

/**
 * Validate SMTP form for testing (without showing errors)
 */
function validateSMTPFormForTest() {
    const host = document.getElementById('smtp_host').value.trim();
    const port = document.getElementById('smtp_port').value.trim();
    const username = document.getElementById('smtp_username').value.trim();
    const password = document.getElementById('smtp_password').value.trim();
    const sslTls = document.querySelector('input[name="ssl_tls"]:checked');

    if (!host || !port || !username || !password || !sslTls) {
        alert('Please fill in all SMTP configuration fields before testing.');
        return false;
    }

    if (!/^\d+$/.test(port) || parseInt(port) < 1 || parseInt(port) > 65535) {
        alert('Please enter a valid port number (1-65535) before testing.');
        return false;
    }

    return true;
}

/**
 * Validate test modal fields
 */
function validateTestModal() {
    let isValid = true;
    clearTestModalErrors();

    const subject = document.getElementById('test_subject').value.trim();
    const email = document.getElementById('test_email').value.trim();
    const content = document.getElementById('test_content').value.trim();

    if (!subject) {
        showTestModalError('test_subject_error', 'Subject is required');
        isValid = false;
    }

    if (!email) {
        showTestModalError('test_email_error', 'Email is required');
        isValid = false;
    }

    if (!content) {
        showTestModalError('test_content_error', 'Message content is required');
        isValid = false;
    }

    return isValid;
}

/**
 * Show test modal error
 */
function showTestModalError(errorId, message) {
    const errorElement = document.getElementById(errorId);
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.classList.remove('hidden');
    }
}

/**
 * Clear test modal errors
 */
function clearTestModalErrors() {
    const errorElements = ['test_subject_error', 'test_email_error', 'test_content_error'];
    errorElements.forEach(errorId => {
        const errorElement = document.getElementById(errorId);
        if (errorElement) {
            errorElement.textContent = '';
            errorElement.classList.add('hidden');
        }
    });
}

/**
 * Send Test Email
 */
async function sendTestEmail() {
    try {
        // Validate modal fields
        if (!validateTestModal()) {
            return;
        }

        // Disable send button and show loading state
        const sendBtn = document.getElementById('sendTestBtn');
        const originalText = sendBtn.textContent;
        sendBtn.disabled = true;
        sendBtn.textContent = 'Sending...';

        // Get SMTP config from form
        const host = document.getElementById('smtp_host').value.trim();
        const port = parseInt(document.getElementById('smtp_port').value.trim());
        const username = document.getElementById('smtp_username').value.trim();
        const password = document.getElementById('smtp_password').value.trim();
        const ssl = document.querySelector('input[name="ssl_tls"]:checked').value === 'true';

        // Get test email data from modal
        const email = document.getElementById('test_email').value.trim();
        const subject = document.getElementById('test_subject').value.trim();
        const content = document.getElementById('test_content').value.trim();

        // Prepare GraphQL mutation
        const mutation = `
            mutation Webhooks_smtp_test_email($input: WebhookSmtpTestEmailInputDto!) {
                webhooks_smtp_test_email(input: $input)
            }
        `;

        const variables = {
            input: {
                smtp_config: {
                    host: host,
                    port: port,
                    ssl: ssl,
                    username: username,
                    password: password
                },
                email: email,
                subject: subject,
                content: content
            }
        };

        console.log('Sending test email:', variables);

        // Use executeGraphQLMutation function
        const data = await executeGraphQLMutation(mutation, variables);
        console.log('Test Email Response:', data);

        // Success
        alert('Test email sent successfully! Please check your inbox.');
        closeTestSMTPModal();

    } catch (error) {
        console.error('Error sending test email:', error);
        alert('Failed to send test email: ' + error.message);
    } finally {
        // Re-enable send button
        const sendBtn = document.getElementById('sendTestBtn');
        sendBtn.disabled = false;
        sendBtn.textContent = 'Send Test';
    }
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('testSMTPModal');
    if (event.target === modal) {
        closeTestSMTPModal();
    }
});
</script>

<?php include get_stylesheet_directory() . '/customer/footer-customer.php'; ?>
