<?php include get_stylesheet_directory() . '/customer/header-customer.php'; ?>
<?php include get_stylesheet_directory() . '/customer/header-dashboard.php'; ?>

<style>
/* Custom dropdown arrow styling */
.select-custom {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 16px center;
    background-repeat: no-repeat;
    background-size: 20px 20px;
}

.select-custom:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%232BA990' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
}

/* Template selection styling */
.template-card {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.template-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-card.selected {
    border-color: #2BA990;
    box-shadow: 0 4px 12px rgba(43, 169, 144, 0.2);
}

.template-card.selected::after {
    content: '✓';
    position: absolute;
    top: 8px;
    right: 8px;
    background: #2BA990;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
}

/* Ensure selected button styling takes precedence */
.template-choose-btn.bg-brand-main {
    background-color: #2BA990 !important;
    border-color: #2BA990 !important;
    color: white !important;
}

.template-choose-btn.bg-brand-main:hover {
    background-color: #2BA990 !important;
    opacity: 0.9;
}

/* Loading Overlay Styles */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-content {
    background: white;
    padding: 40px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #2BA990;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-title {
    font-size: 24px;
    font-weight: bold;
    color: #0A1734;
    margin-bottom: 10px;
}

.loading-message {
    font-size: 16px;
    color: #666;
    margin-bottom: 20px;
}

.loading-steps {
    text-align: left;
    margin-top: 20px;
}

.loading-step {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
}

.loading-step-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.loading-step.completed .loading-step-icon {
    background: #2BA990;
    color: white;
}

.loading-step.active .loading-step-icon {
    background: #2BA990;
    color: white;
    animation: pulse 1.5s ease-in-out infinite;
}

.loading-step.pending .loading-step-icon {
    background: #f3f3f3;
    color: #999;
    border: 2px solid #ddd;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
</style>
<div class="flex min-h-screen bg-neutral-light">
   <?php include get_stylesheet_directory() . '/customer/sidebar.php'; ?>
   <div class="flex-1 flex flex-col min-h-screen">
       
      
      <!-- Website -->
      <main class="flex-1 p-4 bg-db-dark-2">
            <nav class="" aria-label="Breadcrumb">
               <ol class="flex items-center space-x text-sm list-none ml-0 mb-1">
                  <li>
                     <a href="<?php echo site_url('/customer/smtp_config'); ?>" class="font-normal hover:text-db-text text-db-text  text-14 ">Settings</a>
                  </li>
                  <li>
                     <span class="text-neutral-400 text-12">
                        <svg width="22" height="30" viewBox="0 0 22 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path fill-rule="evenodd" clip-rule="evenodd" d="M8 8.7L12.6 15L8 21.3L8.8 22L14 15L8.8 8L8 8.7Z" fill="#000624" fill-opacity="0.3"/>
                        </svg>
                     </span>
                  </li>
                  <li>
                     <a href="#" class="font-normal hover:text-db-text text-db-text text-14">SMTP Configuration</a>
                  </li>
                  
               </ol>
            </nav>

         <!-- Page Title -->
         <h1 class="text-2xl font-semibold text-gray-900 mb-8">SMTP Configuration</h1>


         
         <div class="bg-white rounded-lg shadow p-4">
          
            <!-- Danh sách website sẽ render ở đây -->
            <!-- <div class="text-neutral-medium text-center py-12">No websites found.</div> -->
            <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
               <!-- Cột trái: 4/12 -->
               <div class="xl:col-span-12 col-span-12 relative overflow-hidden min-h-[220px] flex flex-col justify-start">
                  <form class="flex flex-col gap-8 mt-2" id="formCreateWebsite">
                     <!-- BSMTP Host -->
                     <div class="flex flex-col gap-2">
                        <label for="business_name" class="text-primary-main text-base font-medium">
                          SMTP Host
                        </label>
                        <input type="text" 
                           id="business_name" 
                           name="business_name" 
                           placeholder="smtp.google.com"
                           class="w-full !px-[16px] !py-[12px] !h-[48px] rounded-lg !bg-ipt-bg-1
                                    focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                    placeholder:text-gray-400
                                    transition-all duration-200">
                     </div>

                     

                     

                     <!--  SMTP Port -->
                     <div class="flex flex-col gap-2">
                        <label for="business_phone" class="text-primary-main text-base font-medium">
                           SMTP Port
                        </label>
                        <input type="text" 
                           id="business_phone" 
                           name="business_phone" 
                           placeholder="576"
                           class="w-full !px-[16px] !py-[12px] !h-[48px] rounded-lg !bg-ipt-bg-1
                                    focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                    placeholder:text-gray-400
                                    transition-all duration-200">
                     </div>

                     <!--  SSL/TLS -->
                     <div class="flex flex-col gap-2">
                        <label for="business_address" class="text-primary-main text-base font-medium">
                          SSL/TLS
                        </label>
                        <input type="text" 
                           id="business_address" 
                           name="business_address" 
                           placeholder="tls"
                           class="w-full !px-[16px] !py-[12px] !h-[48px] rounded-lg !bg-ipt-bg-1
                                    focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                    placeholder:text-gray-400
                                    transition-all duration-200">
                     </div>
                     <!-- SMTP Username -->
                     <div class="flex flex-col gap-2">
                        <label for="business_address" class="text-primary-main text-base font-medium">
                         SMTP Username
                        </label>
                        <input type="text" 
                           id="business_address" 
                           name="business_address" 
                           placeholder="tls"
                           class="w-full !px-[16px] !py-[12px] !h-[48px] rounded-lg !bg-ipt-bg-1
                                    focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                    placeholder:text-gray-400
                                    transition-all duration-200">
                     </div>
                     <!-- SMTP Password -->
                     <div class="flex flex-col gap-2">
                        <label for="business_address" class="text-primary-main text-base font-medium">
                          SMTP Password
                        </label>
                        <input type="password" 
                           id="business_address" 
                           name="business_address" 
                           placeholder="tls"
                           class="w-full !px-[16px] !py-[12px] !h-[48px] rounded-lg !bg-ipt-bg-1
                                    focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                    placeholder:text-gray-400
                                    transition-all duration-200">
                     </div>
                     <div class="flex flex-col gap-2 justify-between">
                        <button type="button"
                                       id=""
                                       class="flex-1 py-3 px-6 rounded-lg bg-brand-main hover:bg-gray-50  text-primary-main font-semibold
                                          transition-all duration-200"
                                       onclick="createWebsite()"
                                       >
                              Test SMTP
                        </button>
                        <button type="button"
                                 id=""
                                 class="flex-1 py-3 px-6 rounded-lg bg-brand-main hover:bg-brand-main/80 text-primary-main font-semibold
                                    transition-all duration-200"
                                 onclick="createWebsite()"
                                 >
                           Save
                        </button>
                     </div>
                  </form>
               </div>

           
         </div>

         
      </main>

   </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay" style="display: none;">
   <div class="loading-content">
      <div class="loading-spinner"></div>
      <div class="loading-title">Creating Your Website</div>
      <div class="loading-message" id="loadingMessage">Please wait while we set up your website...</div>

      <div class="loading-steps">
         <div class="loading-step pending" id="step-auth">
            <div class="loading-step-icon">1</div>
            <span>Authenticating...</span>
         </div>
         <div class="loading-step pending" id="step-create">
            <div class="loading-step-icon">2</div>
            <span>Creating website...</span>
         </div>
         <div class="loading-step pending" id="step-deploy">
            <div class="loading-step-icon">3</div>
            <span>Deploying website...</span>
         </div>
         <div class="loading-step pending" id="step-setup">
            <div class="loading-step-icon">4</div>
            <span>Setting up website...</span>
         </div>
      </div>
   </div>
</div>

<script>
function toggleMenu(id) {
  const menu = document.getElementById(id);
  menu.classList.toggle('hidden');
}

// Search input clear button functionality (copied from dashboard.php)
document.addEventListener('DOMContentLoaded', function() {
   const searchInput = document.getElementById('search-template');
   const clearButton = document.getElementById('clearButton');

   if (searchInput && clearButton) {
      // Show/hide clear button based on input content
      function toggleClearButton() {
         if (searchInput.value.trim() !== '') {
            clearButton.classList.remove('hidden');
         } else {
            clearButton.classList.add('hidden');
         }
      }

      // Listen for input changes
      searchInput.addEventListener('input', toggleClearButton);
      searchInput.addEventListener('keyup', toggleClearButton);
      searchInput.addEventListener('paste', function() {
         // Delay to allow paste content to be processed
         setTimeout(toggleClearButton, 10);
      });

      // Clear button click handler
      clearButton.addEventListener('click', function() {
         searchInput.value = '';
         searchInput.focus();
         toggleClearButton();

         // Trigger search functionality if needed
         // You can add custom search reset logic here
         console.log('Template search input cleared');

         // If there's a search function, you can call it here
         // For example: performSearch('');
      });

      // Initial check on page load
      toggleClearButton();
   }

   // Next button visibility logic
   function checkFormCompletion() {
      const businessName = document.getElementById('business_name').value.trim();
      // const businessDesc = document.getElementById('business_description').value.trim();
      const businessPhone = document.getElementById('business_phone').value.trim();
      const businessAddress = document.getElementById('business_address').value.trim();
      const selectedTemplate = document.querySelector('.template-card.selected');
      const nextBtn = document.getElementById('continueBtn');

      console.log('Form check:', {
         businessName: businessName,
         businessPhone: businessPhone,
         businessAddress: businessAddress,
         selectedTemplate: selectedTemplate ? 'YES' : 'NO'
      });

      // Show Next button only if all form fields are filled and template is selected
      if (businessName !== '' && businessPhone !== '' && businessAddress !== '' && selectedTemplate) {
         nextBtn.classList.remove('hidden');
         console.log('✅ Next button shown');
      } else {
         nextBtn.classList.add('hidden');
         console.log('❌ Next button hidden');
      }
   }

   // Listen for form field changes
   document.getElementById('business_name').addEventListener('input', checkFormCompletion);
   document.getElementById('business_phone').addEventListener('input', checkFormCompletion);
   document.getElementById('business_address').addEventListener('input', checkFormCompletion);

   // Listen for template selection
   document.addEventListener('click', function(e) {
      // Check if clicked element is a choose button or template card
      const chooseBtn = e.target.closest('.template-choose-btn');
      const templateCard = e.target.closest('.template-card');

      if (chooseBtn || templateCard) {
         // Remove previous selection from all cards
         document.querySelectorAll('.template-card').forEach(card => {
            card.classList.remove('selected');
            // Reset all buttons to "Choose" state
            const btn = card.querySelector('.template-choose-btn');
            if (btn) {
               btn.classList.remove('bg-brand-main', 'text-white', 'border-brand-main');
               btn.classList.add('border-fourth-main', 'text-neutral-strong');
               btn.querySelector('.choose-text').classList.remove('hidden');
               btn.querySelector('.selected-text').classList.add('hidden');
            }
         });

         // Add selection to clicked template
         const currentCard = chooseBtn ? chooseBtn.closest('.template-card') : templateCard;
         currentCard.classList.add('selected');

         // Update button to "Selected" state
         const currentBtn = currentCard.querySelector('.template-choose-btn');
         if (currentBtn) {
            currentBtn.classList.remove('border-fourth-main', 'text-neutral-strong');
            currentBtn.classList.add('bg-brand-main', 'text-white', 'border-brand-main');
            currentBtn.querySelector('.choose-text').classList.add('hidden');
            currentBtn.querySelector('.selected-text').classList.remove('hidden');
         }

         // Check form completion after template selection
         checkFormCompletion();
      }
   });

   // Initial check on page load
   checkFormCompletion();
});

// Website creation functions
async function createWebsite() {
   try {
      // Show loading overlay and prevent navigation
      showLoadingOverlay();
      preventNavigation(true);

      // Get form data
      const businessName = document.getElementById('business_name').value.trim();
      const businessPhone = document.getElementById('business_phone').value.trim();
      const businessAddress = document.getElementById('business_address').value.trim();
      const selectedTemplate = document.querySelector('.template-card.selected');

      if (!selectedTemplate) {
         throw new Error('Please select a template');
      }

      const templateId = selectedTemplate.getAttribute('data-template-id');

      // Get current user's customer ID
      const customerId = <?php echo json_encode(ipt_home_get_customer_id_from_user(get_current_user_id())); ?>;

      if (!customerId) {
         throw new Error('Customer ID not found. Please login again.');
      }

      console.log('Creating website with:', {
         customerId,
         templateId,
         businessName,
         businessPhone,
         businessAddress
      });

      // Step 1: Get authentication token
      updateLoadingStep('step-auth', 'active');
      updateLoadingMessage('Authenticating with server...');
      const authToken = await getAuthToken();
      updateLoadingStep('step-auth', 'completed');

      // Step 2: Create website
      updateLoadingStep('step-create', 'active');
      updateLoadingMessage('Creating your website...');
      const websiteResult = await createWebsiteAPI(authToken, customerId, templateId, businessName, businessPhone, businessAddress);
      updateLoadingStep('step-create', 'completed');

      // Step 3: Deploy website
      updateLoadingStep('step-deploy', 'active');
      updateLoadingMessage('Deploying your website...');
      await deployWebsite(websiteResult.id);
      updateLoadingStep('step-deploy', 'completed');

      // Step 4: Check WordPress status and wait for completion
      updateLoadingStep('step-setup', 'active');
      updateLoadingMessage('Setting up website environment...');
      await checkWordPressStatusUntilReady(websiteResult.domain);
      updateLoadingStep('step-setup', 'completed');

      // Success - redirect to customer website page
      updateLoadingMessage('Website created successfully! Redirecting...');
      setTimeout(() => {
         hideLoadingOverlay();
         preventNavigation(false);
         window.location.href = '/customer/website/';
      }, 2000);

   } catch (error) {
      console.error('Website creation failed:', error);
      hideLoadingOverlay();
      preventNavigation(false);
      alert('Failed to create website: ' + error.message);
   }
}

// Step 1: Get authentication token
async function getAuthToken() {
   const mutation = `
      mutation Auth_login($body: LoginInputDto!) {
         auth_login(body: $body) {
            id
            token {
               access_token
            }
         }
      }
   `;

   const variables = {
      body: {
         email: "<EMAIL>",
         password: "Abc!23456789",
         role_id: 1
      }
   };

   return new Promise((resolve, reject) => {
      jQuery.ajax({
         url: iptHomeAjax.ajax_url,
         type: 'POST',
         dataType: 'json',
         data: {
            action: 'ipt_home_graphql',
            query: mutation,
            variables: JSON.stringify(variables)
         },
         success: function(response) {
            if (response.errors && response.errors.length > 0) {
               reject(new Error(response.errors[0].message || 'Authentication failed'));
            } else if (response.data && response.data.auth_login && response.data.auth_login.token) {
               resolve(response.data.auth_login.token.access_token);
            } else {
               reject(new Error('Invalid authentication response'));
            }
         },
         error: function(xhr, status, error) {
            console.error('Auth AJAX Error:', error);
            reject(new Error('Failed to authenticate'));
         }
      });
   });
}

// Step 2: Create website with Bearer token
async function createWebsiteAPI(authToken, customerId, templateId, businessName, businessPhone, businessAddress) {
   // Generate domain name (you can customize this logic)
   const domainName = `${businessName.toLowerCase().replace(/[^a-z0-9]/g, '')}-${templateId}.reviewsieuxe.com`;

   // Prepare info object
   const info = {
      domain: domainName,
      template_id: parseInt(templateId),
      company_name: businessName,
      phone: businessPhone,
      address: businessAddress
   };

   const mutation = `
      mutation Websites_create($input: WebsiteSaveInputDto!) {
         websites_create(input: $input) {
            id
            created_by
            updated_by
            created_at
            updated_at
            customer_id
            template_id
            domain
            info
            status_id
         }
      }
   `;

   const variables = {
      input: {
         customer_id: parseInt(customerId),
         template_id: parseInt(templateId),
         domain: domainName,
         info: JSON.stringify(info)
      }
   };

   return new Promise((resolve, reject) => {
      jQuery.ajax({
         url: '<?php echo GRAPHQL_API_URL; ?>',
         type: 'POST',
         dataType: 'json',
         headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + authToken
         },
         data: JSON.stringify({
            query: mutation,
            variables: variables
         }),
         success: function(response) {
            if (response.errors && response.errors.length > 0) {
               reject(new Error(response.errors[0].message || 'Website creation failed'));
            } else if (response.data && response.data.websites_create && response.data.websites_create.id) {
               console.log('Website created successfully:', response.data.websites_create);
               resolve({
                  id: response.data.websites_create.id,
                  domain: domainName
               });
            } else {
               reject(new Error('Invalid website creation response'));
            }
         },
         error: function(xhr, status, error) {
            console.error('Website creation AJAX Error:', error);
            reject(new Error('Failed to create website'));
         }
      });
   });
}

// Step 3: Deploy website using hardcoded token
async function deployWebsite(websiteId) {
   const mutation = `
      mutation Webhooks_websites_deploy_by_id($id: Int!) {
         webhooks_websites_deploy_by_id(id: $id)
      }
   `;

   const variables = {
      id: parseInt(websiteId)
   };

   return new Promise((resolve, reject) => {
      jQuery.ajax({
         url: iptHomeAjax.ajax_url,
         type: 'POST',
         dataType: 'json',
         data: {
            action: 'ipt_home_graphql',
            query: mutation,
            variables: JSON.stringify(variables)
         },
         success: function(response) {
            if (response.errors && response.errors.length > 0) {
               reject(new Error(response.errors[0].message || 'Website deployment failed'));
            } else if (response.data && response.data.webhooks_websites_deploy_by_id === true) {
               console.log('Website deployed successfully');
               resolve(true);
            } else {
               reject(new Error('Invalid deployment response'));
            }
         },
         error: function(xhr, status, error) {
            console.error('Deployment AJAX Error:', error);
            reject(new Error('Failed to deploy website'));
         }
      });
   });
}

// Step 4: Check WordPress status until ready
async function checkWordPressStatusUntilReady(domain) {
   return new Promise((resolve, reject) => {
      console.log('=== CHECKING WORDPRESS STATUS ===');
      console.log('Domain:', domain);

      // Start polling every 5 seconds
      const pollInterval = setInterval(() => {
         checkWordPressStatus(domain, pollInterval, resolve, reject);
      }, 5000);

      // Initial check
      checkWordPressStatus(domain, pollInterval, resolve, reject);

      // Set timeout after 5 minutes
      setTimeout(() => {
         clearInterval(pollInterval);
         reject(new Error('Website setup timeout after 5 minutes'));
      }, 300000);
   });
}

// Function to check WordPress status
function checkWordPressStatus(domain, pollInterval, resolve, reject) {
   console.log('=== CHECKING WORDPRESS STATUS ===');
   console.log('Domain:', domain);

   // Prepare GraphQL query
   const query = `
      query Wordpress_status($domain: String!) {
         wordpress_status(domain: $domain)
      }
   `;

   // Prepare variables
   const variables = {
      domain: domain
   };

   console.log('WordPress status query:', query);
   console.log('WordPress status variables:', variables);

   // Make API call using existing GraphQL system (uses GRAPHQL_TOKEN)
   jQuery.ajax({
      url: iptHomeAjax.ajax_url,
      type: 'POST',
      dataType: 'json',
      data: {
         action: 'ipt_home_graphql',
         query: query,
         variables: JSON.stringify(variables)
      },
      success: function(response) {
         console.log('=== WORDPRESS STATUS RESPONSE ===');
         console.log(response);

         if (response.errors && response.errors.length > 0) {
            console.error('WordPress status errors:', response.errors);
            // Continue polling even on error
         } else if (response.data && response.data.wordpress_status) {
            const wpStatus = response.data.wordpress_status;
            console.log('WordPress status data:', wpStatus);

            // Check if wp_password has value
            if (wpStatus.wp_password && wpStatus.wp_password.trim() !== '') {
               console.log('✅ WordPress setup complete! Password found:', wpStatus.wp_password);

               // Stop polling
               clearInterval(pollInterval);

               // Resolve the promise
               resolve(wpStatus);
            } else {
               console.log('⏳ WordPress still setting up... wp_password not ready yet');
            }
         } else {
            console.error('Unexpected WordPress status response:', response);
         }
      },
      error: function(xhr, status, error) {
         console.error('WordPress status AJAX Error:', error);
         console.error('Response:', xhr.responseText);
         // Continue polling even on error
      }
   });
}

// Loading overlay functions
function showLoadingOverlay() {
   document.getElementById('loadingOverlay').style.display = 'flex';
   document.body.style.overflow = 'hidden'; // Prevent scrolling
}

function hideLoadingOverlay() {
   document.getElementById('loadingOverlay').style.display = 'none';
   document.body.style.overflow = 'auto'; // Restore scrolling
}

function updateLoadingMessage(message) {
   document.getElementById('loadingMessage').textContent = message;
}

function updateLoadingStep(stepId, status) {
   const step = document.getElementById(stepId);
   if (step) {
      // Remove all status classes
      step.classList.remove('pending', 'active', 'completed');
      // Add new status
      step.classList.add(status);

      // Update icon for completed steps
      if (status === 'completed') {
         const icon = step.querySelector('.loading-step-icon');
         icon.innerHTML = '✓';
      }
   }
}

// Navigation prevention functions
let navigationBlocked = false;

function preventNavigation(block) {
   navigationBlocked = block;

   if (block) {
      // Prevent page unload/refresh
      window.addEventListener('beforeunload', handleBeforeUnload);

      // Prevent back/forward navigation
      window.addEventListener('popstate', handlePopState);

      // Add history state to prevent back button
      history.pushState(null, null, window.location.href);
   } else {
      // Remove event listeners
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('popstate', handlePopState);
   }
}

function handleBeforeUnload(e) {
   if (navigationBlocked) {
      const message = 'Your website is being created. Are you sure you want to leave?';
      e.preventDefault();
      e.returnValue = message;
      return message;
   }
}

function handlePopState(e) {
   if (navigationBlocked) {
      // Push the state back to prevent navigation
      history.pushState(null, null, window.location.href);

      // Show confirmation
      if (confirm('Your website is being created. Are you sure you want to leave?')) {
         preventNavigation(false);
         hideLoadingOverlay();
         history.back();
      }
   }
}
</script>

<?php include get_stylesheet_directory() . '/customer/footer-customer.php'; ?>
